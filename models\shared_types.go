package models

// FotoMetadata armazena metadados de um arquivo de foto.
type FotoMetadata struct {
	FileID       string `json:"file_id,omitempty" bson:"file_id,omitempty"`         // ID usado para nomear o arquivo base
	Extension    string `json:"extension,omitempty" bson:"extension,omitempty"`     // Ex: .jpg, .png
	ContentType  string `json:"content_type,omitempty" bson:"content_type,omitempty"` // Ex: image/jpeg
	RelativePath string `json:"-" bson:"relative_path,omitempty"`                   // Caminho no disco a partir do diretório de upload específico (ex: <tipo_entidade>/fotos/file_id.extension)
	URL          string `json:"url,omitempty" bson:"url,omitempty"`                 // URL completa, agora persistida
}
