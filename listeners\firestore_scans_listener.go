package listeners

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"wedriverapigo/models" // Adicionado para ScanDataMongoDB

	"cloud.google.com/go/firestore"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"google.golang.org/api/iterator"
)

// markDocumentAsProcessed atualiza o documento no Firestore para processed: true e define updatedAt.
func markDocumentAsProcessed(ctx context.Context, docRef *firestore.DocumentRef) {
	now := time.Now().UTC()
	_, err := docRef.Set(ctx, map[string]interface{}{
		"processed": true,
		"updatedAt": now,
	}, firestore.MergeAll) // MergeAll para não sobrescrever outros campos do documento.

	if err != nil {
		log.Printf("[Firestore Listener] Erro ao marcar documento %s como processado: %v", docRef.ID, err)
	} else {
		log.Printf("[Firestore Listener] Documento %s marcado como processado e updatedAt definido.", docRef.ID)
	}
}

// buscarDadosMotorista busca os dados do motorista no MongoDB pelo userMID para criar o log de auditoria.
// Retorna os dados do motorista ou nil se não encontrado/erro.
func buscarDadosMotorista(ctx context.Context, mongoClient *mongo.Client, dbName string, userMID string) *models.MotoristaLogData {
	// Validar se userMID não está vazio
	if userMID == "" {
		return nil
	}

	// Converter userMID string para ObjectID
	motoristaID, err := primitive.ObjectIDFromHex(userMID)
	if err != nil {
		log.Printf("[MongoDB Sync] Erro ao converter userMID '%s' para ObjectID: %v", userMID, err)
		return nil
	}

	// Buscar motorista no MongoDB
	motoristasCollection := mongoClient.Database(dbName).Collection("motoristas")

	// Criar contexto com timeout específico para esta operação
	ctxTimeout, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	var motorista models.Motorista
	filter := bson.M{"_id": motoristaID}

	err = motoristasCollection.FindOne(ctxTimeout, filter).Decode(&motorista)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			log.Printf("[MongoDB Sync] Motorista com ID '%s' não encontrado para log de auditoria", userMID)
		} else {
			log.Printf("[MongoDB Sync] Erro ao buscar motorista '%s' para log de auditoria: %v", userMID, err)
		}
		return nil
	}

	// Criar dados de log do motorista
	motoristaLog := &models.MotoristaLogData{
		Nome: motorista.Nome,
		CPF:  motorista.CPF,
	}

	log.Printf("[MongoDB Sync] Dados do motorista '%s' (%s) adicionados ao log de auditoria", motorista.Nome, motorista.CPF)
	return motoristaLog
}

// saveDataToMongoDB mapeia os dados do Firestore para ScanDataMongoDB e insere no MongoDB.
func saveDataToMongoDB(ctx context.Context, mongoClient *mongo.Client, dbName string, targetCollectionName string, firestoreDocID string, data map[string]interface{}) {
	mongoDoc := models.ScanDataMongoDB{
		ID: firestoreDocID, // Usar o ID do Firestore como _id no MongoDB
	}

	if val, ok := data["createdAt"]; ok {
		switch v := val.(type) {
		case string:
			t, err := time.Parse(time.RFC3339Nano, v)
			if err == nil {
				mongoDoc.CreatedAt = t
			} else {
				log.Printf("[MongoDB Sync] Erro ao parsear 'createdAt' (string) para o doc %s: %v", firestoreDocID, err)
			}
		case time.Time:
			mongoDoc.CreatedAt = v
		default:
			log.Printf("[MongoDB Sync] Tipo inesperado para 'createdAt' no doc %s: %T", firestoreDocID, v)
		}
	}
	if val, ok := data["descricao"]; ok {
		if v, asserted := val.(string); asserted {
			mongoDoc.Descricao = v
		}
	}
	if val, ok := data["syncedAt"]; ok {
		switch v := val.(type) {
		case string:
			t, err := time.Parse(time.RFC3339Nano, v)
			if err == nil {
				mongoDoc.SyncedAt = &t
			} else {
				log.Printf("[MongoDB Sync] Erro ao parsear 'syncedAt' (string) para o doc %s: %v", firestoreDocID, err)
			}
		case time.Time:
			mongoDoc.SyncedAt = &v
		default:
			log.Printf("[MongoDB Sync] Tipo inesperado para 'syncedAt' no doc %s: %T", firestoreDocID, v)
		}
	}
	if val, ok := data["id"]; ok { // Este é o 'originalId'
		if v, asserted := val.(string); asserted { // Firestore pode enviar números como float64
			mongoDoc.OriginalId = v
		}
	}
	if val, ok := data["location"]; ok {
		if locMap, asserted := val.(map[string]interface{}); asserted {
			locationData := models.LocationData{}
			if lat, okLat := locMap["latitude"].(float64); okLat {
				locationData.Latitude = lat
			}
			if lon, okLon := locMap["longitude"].(float64); okLon {
				locationData.Longitude = lon
			}
			mongoDoc.Location = &locationData
		}
	}
	if val, ok := data["mac"]; ok {
		if v, asserted := val.(string); asserted {
			mongoDoc.Mac = v
		}
	}
	if val, ok := data["modelo"]; ok {
		if v, asserted := val.(string); asserted {
			mongoDoc.Modelo = v
		}
	}
	if val, ok := data["placa"]; ok {
		if v, asserted := val.(string); asserted {
			mongoDoc.Placa = v
		}
	}
	if val, ok := data["processed"]; ok {
		if v, asserted := val.(bool); asserted {
			mongoDoc.Processed = v // Deve ser false quando recebido pelo listener
		}
	}
	if val, ok := data["type"]; ok {
		if v, asserted := val.(string); asserted {
			mongoDoc.Type = v
		}
	}
	if val, ok := data["typeScan"]; ok {
		if v, asserted := val.(string); asserted {
			mongoDoc.TypeScan = v
		}
	}
	if val, ok := data["userMID"]; ok {
		if v, asserted := val.(string); asserted {
			mongoDoc.UserId = v

			// Buscar dados do motorista para log de auditoria (desnormalização)
			if v != "" {
				log.Printf("[MongoDB Sync] Buscando dados do motorista '%s' para log de auditoria...", v)
				motoristaLog := buscarDadosMotorista(ctx, mongoClient, dbName, v)
				if motoristaLog != nil {
					mongoDoc.MotoristaLog = motoristaLog
					log.Printf("[MongoDB Sync] Log do motorista adicionado: Nome='%s', CPF='%s'", motoristaLog.Nome, motoristaLog.CPF)
				} else {
					log.Printf("[MongoDB Sync] Não foi possível obter dados do motorista '%s' para log de auditoria", v)
				}
			}
		}
	}
	// Mapear campo vehicleId do Firebase para MongoDB (seguindo padrão do userMID)
	if val, ok := data["vehicleId"]; ok {
		if v, asserted := val.(string); asserted {
			mongoDoc.VehicleId = v
			log.Printf("[MongoDB Sync] Campo vehicleId mapeado: '%s'", v)
		}
	}
	// UpdatedAt original do Firestore, se existir
	if val, ok := data["updatedAt"]; ok {
		if v, asserted := val.(time.Time); asserted {
			mongoDoc.UpdatedAt = &v
		}
	}

	collection := mongoClient.Database(dbName).Collection(targetCollectionName)
	_, err := collection.InsertOne(ctx, mongoDoc)
	if err != nil {
		log.Printf("[MongoDB Sync] Erro ao inserir documento %s na coleção '%s': %v", firestoreDocID, targetCollectionName, err)
		// Considerar uma estratégia de retry ou dead-letter queue aqui
	} else {
		// Log detalhado com informações do motorista e veículo
		logParts := []string{fmt.Sprintf("Documento %s inserido com sucesso na coleção '%s'", firestoreDocID, targetCollectionName)}

		if mongoDoc.MotoristaLog != nil {
			logParts = append(logParts, fmt.Sprintf("motorista '%s'", mongoDoc.MotoristaLog.Nome))
		}

		if mongoDoc.VehicleId != "" {
			logParts = append(logParts, fmt.Sprintf("vehicleId '%s'", mongoDoc.VehicleId))
		}

		log.Printf("[MongoDB Sync] %s", strings.Join(logParts, " com "))
	}
}

// GenericFirestoreListener ouve uma coleção específica no Firestore, filtra por 'processed == false',P
// e marca os documentos como processados após o tratamento inicial (atualmente, logging).
// Futuramente, irá interagir com coleções correspondentes no MongoDB.
func GenericFirestoreListener(ctx context.Context, client *firestore.Client, dbName string, mongoClient *mongo.Client, firestoreCollectionToListen string, mongoTargetCollection string) {
	log.Printf("Iniciando listener para a coleção '%s' no Firestore (filtrando por 'processed == false')...", firestoreCollectionToListen)

	// Coleção do Firestore que estamos ouvindo
	firestoreCollection := client.Collection(firestoreCollectionToListen)
	// Aplicar filtro para buscar apenas documentos onde 'processed' é false
	firestoreQuery := firestoreCollection.Where("processed", "==", false)

	// Loop principal para manter o listener ativo e tentar reconectar em caso de erro.
	for {
		// Verifica se o contexto foi cancelado antes de tentar (re)estabelecer o listener
		select {
		case <-ctx.Done():
			log.Printf("Contexto cancelado antes de (re)iniciar o listener para '%s'. Encerrando.", firestoreCollectionToListen)
			return
		default:
			// Contexto ainda ativo, prossegue.
		}

		it := firestoreQuery.Snapshots(ctx) // Usar a query filtrada
		// Defer Stop() dentro do loop para que seja chamado para cada novo iterador criado.
		// Isso é importante para liberar recursos se o loop externo continuar.
		stopIter := func() {
			if it != nil {
				it.Stop()
			}
		}
		defer stopIter() // Será chamado quando ListenToScansCollection retornar.

		log.Printf("Listener de snapshots para '%s' estabelecido.", firestoreCollectionToListen)

		// Loop interno para processar snapshots do iterador atual.
		processSnapshots := true
		for processSnapshots {
			snap, err := it.Next()
			if err == iterator.Done {
				log.Printf("Listener da coleção '%s' encerrado (iterator.Done). O loop externo tentará reconectar se o contexto ainda estiver ativo.", firestoreCollectionToListen)
				processSnapshots = false // Sai do loop interno, o loop externo tentará reconectar.
				break
			}
			if err != nil {
				log.Printf("Erro ao obter snapshot da coleção '%s': %v. O loop externo tentará reconectar.", firestoreCollectionToListen, err)
				processSnapshots = false // Sai do loop interno, o loop externo tentará reconectar.
				break
			}

			if snap == nil || (snap.Size == 0 && len(snap.Changes) == 0) {
				continue
			}

			log.Printf("Recebido snapshot da coleção '%s' às %s com %d mudanças.", firestoreCollectionToListen, snap.ReadTime, len(snap.Changes))

			for _, change := range snap.Changes {
				docData := change.Doc.Data()
				docID := change.Doc.Ref.ID

				switch change.Kind {
				case firestore.DocumentAdded:
					log.Printf("[Firestore Listener] Novo documento adicionado em '%s' (ID: %s): %v", firestoreCollectionToListen, docID, docData)
					if mongoTargetCollection == "identificacoes" { // Processar somente para a coleção 'scans' -> 'identificacoes'
						saveDataToMongoDB(ctx, mongoClient, dbName, mongoTargetCollection, docID, docData)
					} else {
						log.Printf("[MongoDB Sync] Sincronização para a coleção '%s' (MongoDB: '%s') ainda não implementada.", firestoreCollectionToListen, mongoTargetCollection)
					}
					markDocumentAsProcessed(ctx, change.Doc.Ref) // Marcar como processado
				case firestore.DocumentModified:
					// Documentos modificados que agora correspondem à query (processed: false) são tratados como novos itens a serem processados.
					log.Printf("[Firestore Listener] Documento modificado (agora corresponde à query) em '%s' (ID: %s): %v", firestoreCollectionToListen, docID, docData)
					// TODO: Implementar updateDataInMongoDB (ou tratar como save se não existir no MongoDB, usando mongoTargetCollection)
					markDocumentAsProcessed(ctx, change.Doc.Ref) // Marcar como processado
				case firestore.DocumentRemoved:
					// Este caso geralmente não será acionado se o filtro for 'processed == false',
					// a menos que o documento seja excluído diretamente do Firestore.
					// Se um doc é mudado de processed:false para processed:true, ele desaparece da query,
					// o que é efetivamente uma remoção do conjunto de resultados do snapshot.
					log.Printf("[Firestore Listener] Documento removido do escopo da query em '%s' (ID: %s)", firestoreCollectionToListen, docID)
					// TODO: Implementar removeDataFromMongoDB(ctx, mongoClient, dbName, mongoTargetCollection, docID)
				default:
					log.Printf("[Firestore Listener] Tipo de mudança desconhecido (%d) para o documento %s em '%s'", change.Kind, docID, firestoreCollectionToListen)
				}
			}
		}

		// Limpa o iterador atual antes de potencialmente criar um novo no loop externo.
		stopIter()
		it = nil

		// Se o contexto foi cancelado, sai do loop principal de reconexão.
		select {
		case <-ctx.Done():
			log.Printf("Contexto cancelado, encerrando completamente o listener de '%s'.", firestoreCollectionToListen)
			return
		default:
			// Contexto ainda ativo, o loop externo continuará e tentará reconectar o listener.
			log.Printf("Tentando reconectar o listener de '%s' em 5 segundos...", firestoreCollectionToListen)
			time.Sleep(5 * time.Second) // Pausa antes de tentar recriar o snapshot listener
		}
	}
}
