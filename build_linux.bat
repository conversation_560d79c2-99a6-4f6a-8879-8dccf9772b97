@echo off
echo ===================================================
echo WeDriver API - Script de Compilacao para Linux
echo ===================================================
echo.

REM Salvar a configuracao atual do GOOS e GOARCH
FOR /F "tokens=*" %%g IN ('go env GOOS') do (SET ORIGINAL_GOOS=%%g)
FOR /F "tokens=*" %%g IN ('go env GOARCH') do (SET ORIGINAL_GOARCH=%%g)

echo Configuracao original:
echo GOOS=%ORIGINAL_GOOS%
echo GOARCH=%ORIGINAL_GOARCH%
echo.

REM Criar pasta de build se nao existir
if not exist "build" mkdir build
if not exist "build\linux" mkdir build\linux

echo Configurando ambiente para compilacao Linux...
set GOOS=linux
set GOARCH=amd64

echo Compilando para Linux (amd64)...
set CGO_ENABLED=0
go build -o build\linux\wedriverapigo main.go

echo.
echo Compilacao para Linux concluida!
echo Executavel gerado: build\linux\wedriverapigo
echo.

echo.
echo Iniciando upload do artefato Linux para o servidor...
REM Certifique-se de que o comando 'scp' esta no PATH e que as chaves SSH estao configuradas
REM no servidor (root@************) para login sem senha, ou prepare-se para digitar a senha.
REM O OpenSSH (que inclui scp) geralmente esta disponivel no Windows 10 e posterior.
scp build\linux\wedriverapigo root@************:/root/wedrivergoapi/
IF %ERRORLEVEL% NEQ 0 (
    echo ERRO: Falha no upload do arquivo (scp). Verifique a conexao, o comando scp e as credenciais SSH.
) ELSE (
    echo Upload do artefato Linux concluido com sucesso.
)
echo.

echo Restaurando configuracao original...
set GOOS=%ORIGINAL_GOOS%
set GOARCH=%ORIGINAL_GOARCH%

echo Compilando para Windows...
go build -o build\wedriverapigo.exe main.go

echo.
echo Compilacao para Windows concluida!
echo Executavel gerado: build\wedriverapigo.exe
echo.

echo Processo de compilacao finalizado com sucesso!
echo ===================================================

pause
echo EJGmQ5SNjLiUFpk