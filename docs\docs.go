// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/auth/login": {
            "post": {
                "description": "Autentica um usuário com email e senha e retorna tokens JWT.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Autenticação"
                ],
                "summary": "Login de Usuário",
                "parameters": [
                    {
                        "description": "Credenciais de Login",
                        "name": "login",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "token\": \"jwt_token\", \"refresh_token\": \"jwt_refresh_token\", \"expires_at\": 1678886400, \"user_id\": \"user_hex_id\", \"name\": \"User Name\", \"roles\": [\"role1\", \"role2\"], \"empresa_id\": \"empresa_hex_id",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "error\": \"Dados inválidos: ...",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "error\": \"Credenciais inválidas\" or \"error\": \"Usuário não está ativo",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao processar login\" or \"error\": \"Erro ao gerar token de autenticação",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/auth/refresh": {
            "post": {
                "description": "Gera um novo token de acesso JWT usando um refresh token válido.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Autenticação"
                ],
                "summary": "Refresh de Token JWT",
                "parameters": [
                    {
                        "description": "Refresh Token",
                        "name": "refresh",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.RefreshTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "token\": \"new_jwt_token\", \"expires_at\": 1678886400",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "error\": \"Dados inválidos: ...",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "error\": \"Refresh token inválido ou expirado\" or \"error\": \"Usuário não está ativo",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao processar refresh token\" or \"error\": \"Erro ao gerar token de autenticação",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/cadastro/completar": {
            "post": {
                "description": "Finaliza o processo de cadastro utilizando a chave de email validada, nome e senha. Cria a empresa associada e ativa o usuário.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Cadastro"
                ],
                "summary": "Completar Cadastro de Usuário e Empresa",
                "parameters": [
                    {
                        "description": "Dados para completar o cadastro",
                        "name": "completar_cadastro",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.CompletarCadastroRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "message\": \"Cadastro concluído com sucesso!",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "error\": \"Dados inválidos: {detalhe_do_erro}",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "error\": \"Chave de ativação inválida.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "409": {
                        "description": "error\": \"Este cadastro já foi completado anteriormente.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao validar chave de ativação.\" or \"error\": \"Erro ao configurar a conta da empresa.\" or \"error\": \"Erro ao processar informações de segurança.\" or \"error\": \"Erro ao finalizar o cadastro.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/cadastro/iniciar": {
            "post": {
                "description": "Recebe um email, cria um pré-cadastro para o usuário, gera uma chave de confirmação e envia por email.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Cadastro"
                ],
                "summary": "Iniciar Processo de Cadastro",
                "parameters": [
                    {
                        "description": "Email para iniciar o cadastro",
                        "name": "cadastro",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.IniciarCadastroRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "message\": \"Processo de cadastro iniciado. Verifique seu email para o código de confirmação.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "error\": \"Dados inválidos: {detalhe_do_erro}",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "409": {
                        "description": "error\": \"Este email já está cadastrado.\" or \"error\": \"Cadastro pendente para este email...",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao iniciar processo de cadastro.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/cadastro/validar": {
            "post": {
                "description": "Verifica se uma chave de email (enviada anteriormente) é válida, não expirou e não foi utilizada.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Cadastro"
                ],
                "summary": "Validar Chave de Email para Cadastro",
                "parameters": [
                    {
                        "description": "Chave de email para validação",
                        "name": "validacao",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.ValidarCadastroRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "message\": \"Chave de email válida.\", \"email\": \"<EMAIL>",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "error\": \"Dados inválidos: {detalhe_do_erro}",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "error\": \"Chave de ativação inválida.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "409": {
                        "description": "error\": \"Este cadastro já foi completado.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "410": {
                        "description": "error\": \"Chave de ativação expirada.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao validar chave de ativação.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/identificacao": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retorna uma lista de todas as identificações com opções de filtragem. Se nenhuma data for especificada, retorna apenas as identificações do dia atual.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "identificacoes"
                ],
                "summary": "Lista todas as identificações",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Filtrar por ID do motorista (userMID)",
                        "name": "motorista",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filtrar por placa do veículo",
                        "name": "placa",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filtrar por tipo de identificação",
                        "name": "tipo",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filtrar por data de criação (início) no formato YYYY-MM-DD. Se não for fornecido, usa a data atual.",
                        "name": "data_inicio",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filtrar por data de criação (fim) no formato YYYY-MM-DD. Se não for fornecido, usa a data atual.",
                        "name": "data_fim",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "Se true, retorna todas as identificações sem filtro de data. Padrão: false",
                        "name": "todos",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Limite de resultados por página (padrão: 50)",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Número de resultados para pular (para paginação)",
                        "name": "skip",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/identificacao/motorista/{motorista_id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retorna uma lista de identificações filtradas por ID do motorista",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "identificacoes"
                ],
                "summary": "Lista identificações por motorista",
                "parameters": [
                    {
                        "type": "string",
                        "description": "ID do motorista",
                        "name": "motorista_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "Limite de resultados por página (padrão: 50)",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Número de resultados para pular (para paginação)",
                        "name": "skip",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/identificacao/veiculo/{placa}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retorna uma lista de identificações filtradas por placa do veículo",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "identificacoes"
                ],
                "summary": "Lista identificações por veículo",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Placa do veículo",
                        "name": "placa",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "Limite de resultados por página (padrão: 50)",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Número de resultados para pular (para paginação)",
                        "name": "skip",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/identificacao/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retorna os detalhes de uma identificação específica",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "identificacoes"
                ],
                "summary": "Obtém uma identificação pelo ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "ID da identificação",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/motorista": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retorna uma lista paginada de motoristas associados à empresa do usuário autenticado.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Motoristas"
                ],
                "summary": "Listar Motoristas",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Número da página",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "maximum": 100,
                        "type": "integer",
                        "default": 10,
                        "description": "Número de itens por página",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "data\": []models.Motorista, \"total\": int64, \"page\": int, \"limit\": int",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "error\": \"ID da empresa inválido",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "error\": \"ID da empresa não encontrado no token",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao buscar motoristas",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Cria um novo registro de motorista associado à empresa do usuário autenticado.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Motoristas"
                ],
                "summary": "Criar Novo Motorista",
                "parameters": [
                    {
                        "description": "Dados do Motorista para criação",
                        "name": "motorista",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.Motorista"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Motorista criado com sucesso",
                        "schema": {
                            "$ref": "#/definitions/models.Motorista"
                        }
                    },
                    "400": {
                        "description": "error\": \"ID da empresa inválido\" or \"error\": \"Dados inválidos: {detalhe_do_erro}",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "error\": \"ID da empresa não encontrado no token",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao criar motorista",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/motorista/ativar/{id}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Busca um motorista no Firebase pelo ID, copia os dados para o MongoDB e atualiza o Firebase com o ID do MongoDB",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Motoristas"
                ],
                "summary": "Ativar motorista do Firebase",
                "parameters": [
                    {
                        "type": "string",
                        "description": "ID do motorista no Firebase",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Motorista ativado com sucesso",
                        "schema": {
                            "$ref": "#/definitions/models.Motorista"
                        }
                    },
                    "400": {
                        "description": "error\": \"ID não fornecido",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "error\": \"Motorista não encontrado no Firebase",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro interno do servidor",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/motorista/verificar-codigo": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Busca os dados de um pré-cadastro de motorista no Firestore usando um código de verificação.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Motoristas"
                ],
                "parameters": [
                    {
                        "type": "string",
                        "description": "Código de Verificação do Motorista",
                        "name": "code",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Dados do motorista encontrados",
                        "schema": {
                            "$ref": "#/definitions/handlers.VerifiedMotoristaData"
                        }
                    },
                    "400": {
                        "description": "error: \\\"Código de verificação é obrigatório\\\" ou \\\"error\\\": \\\"Formato inválido para o código de verificação\\",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "error: \\\"Não autorizado\\",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "error: \\\"Motorista não encontrado com o código fornecido\\",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error: \\\"Erro ao buscar dados do motorista no Firestore\\",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/motorista/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retorna os detalhes de um motorista específico, dado seu ID, se pertencer à empresa do usuário autenticado.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Motoristas"
                ],
                "summary": "Obter Motorista por ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "ID do Motorista",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Detalhes do Motorista",
                        "schema": {
                            "$ref": "#/definitions/models.Motorista"
                        }
                    },
                    "400": {
                        "description": "error\": \"ID da empresa inválido\" or \"error\": \"ID do motorista inválido",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "error\": \"ID da empresa não encontrado no token",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "error\": \"Motorista não encontrado ou não pertence à empresa",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao buscar motorista",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Atualiza completamente os dados de um motorista existente, dado seu ID. Requer que todos os campos do motorista sejam enviados.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Motoristas"
                ],
                "summary": "Atualizar Motorista (PUT)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "ID do Motorista",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Dados completos do Motorista para atualização",
                        "name": "motorista",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.Motorista"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Motorista atualizado com sucesso",
                        "schema": {
                            "$ref": "#/definitions/models.Motorista"
                        }
                    },
                    "400": {
                        "description": "error\": \"ID da empresa inválido\" or \"error\": \"ID do motorista inválido\" or \"error\": \"Dados inválidos: {detalhe_do_erro}",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "error\": \"ID da empresa não encontrado no token",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "error\": \"Motorista não encontrado ou não pertence à empresa",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao atualizar motorista",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            },
            "patch": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Atualiza parcialmente os dados de um motorista existente, dado seu ID. Apenas os campos fornecidos na requisição serão alterados.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Motoristas"
                ],
                "summary": "Atualizar Parcialmente Motorista (PATCH)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "ID do Motorista",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Campos do Motorista para atualização parcial. Datas devem ser strings no formato RFC3339 (YYYY-MM-DDTHH:MM:SSZ) ou YYYY-MM-DD.",
                        "name": "motorista_update",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Motorista atualizado com sucesso",
                        "schema": {
                            "$ref": "#/definitions/models.Motorista"
                        }
                    },
                    "400": {
                        "description": "error\": \"ID da empresa inválido\" or \"error\": \"ID do motorista inválido\" or \"error\": \"Dados inválidos\" or \"error\": \"Nenhum campo válido para atualização fornecido...\" or \"error\": \"Formato de data inválido...",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "error\": \"ID da empresa não encontrado no token",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "error\": \"Motorista não encontrado ou não pertence à empresa",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao atualizar motorista",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/redefinir-senha/completar": {
            "post": {
                "description": "Finaliza o processo de redefinição de senha, atualizando-a com a nova senha fornecida, utilizando uma chave de reset válida.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Redefinir Senha"
                ],
                "summary": "Completar Redefinição de Senha",
                "parameters": [
                    {
                        "description": "Chave de redefinição e nova senha",
                        "name": "completar_reset",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.CompletarResetSenhaRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "message\": \"Senha redefinida com sucesso!",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "error\": \"Dados inválidos: {detalhe_do_erro}",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "error\": \"Chave de reset inválida, já utilizada ou expirada.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao validar chave de reset.\" or \"error\": \"Erro ao processar informações de segurança.\" or \"error\": \"Erro ao redefinir a senha.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/redefinir-senha/iniciar": {
            "post": {
                "description": "Recebe um email, verifica se está cadastrado e, em caso positivo, envia um email com uma chave para redefinição de senha.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Redefinir Senha"
                ],
                "summary": "Iniciar Processo de Redefinição de Senha",
                "parameters": [
                    {
                        "description": "Email para iniciar a redefinição de senha",
                        "name": "iniciar_reset",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.IniciarResetSenhaRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "message\": \"Se o email estiver cadastrado, você receberá instruções para redefinir sua senha.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "error\": \"Email inválido ou ausente",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao processar a solicitação.\" or \"error\": \"Erro ao enviar email de redefinição de senha.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/redefinir-senha/validar": {
            "post": {
                "description": "Verifica se uma chave de redefinição de senha é válida, não expirou e não foi utilizada.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Redefinir Senha"
                ],
                "summary": "Validar Chave de Redefinição de Senha",
                "parameters": [
                    {
                        "description": "Chave de redefinição para validação",
                        "name": "validar_reset",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.ValidarResetSenhaRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "email\": \"<EMAIL>\", \"name\": \"Nome do Usuario",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "error\": \"Chave de reset inválida ou ausente",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "error\": \"Chave de reset inválida, já utilizada ou expirada.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao validar chave de reset.",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/veiculo": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retorna uma lista paginada de veículos associados à empresa do usuário autenticado.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Veículos"
                ],
                "summary": "Listar Veículos",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Número da página",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "maximum": 100,
                        "type": "integer",
                        "default": 10,
                        "description": "Número de itens por página",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "data\": []models.Veiculo, \"total\": int64, \"page\": int, \"limit\": int",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "error\": \"ID da empresa inválido",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "error\": \"ID da empresa não encontrado no token",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao buscar veículos",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Cria um novo registro de veículo associado à empresa do usuário autenticado.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Veículos"
                ],
                "summary": "Criar Novo Veículo",
                "parameters": [
                    {
                        "description": "Dados do Veículo para criação",
                        "name": "veiculo",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.Veiculo"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Veículo criado com sucesso",
                        "schema": {
                            "$ref": "#/definitions/models.Veiculo"
                        }
                    },
                    "400": {
                        "description": "error\": \"ID da empresa inválido\" or \"error\": \"Dados inválidos: {detalhe_do_erro}",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "error\": \"ID da empresa não encontrado no token",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao criar veículo",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/veiculo/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retorna os detalhes de um veículo específico, dado seu ID, se pertencer à empresa do usuário autenticado.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Veículos"
                ],
                "summary": "Obter Veículo por ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "ID do Veículo",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Detalhes do Veículo",
                        "schema": {
                            "$ref": "#/definitions/models.Veiculo"
                        }
                    },
                    "400": {
                        "description": "error\": \"ID da empresa inválido\" or \"error\": \"ID do veículo inválido",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "error\": \"ID da empresa não encontrado no token",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "error\": \"Veículo não encontrado ou não pertence à empresa",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao buscar veículo",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Atualiza completamente os dados de um veículo existente, dado seu ID. Requer que todos os campos do veículo sejam enviados.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Veículos"
                ],
                "summary": "Atualizar Veículo (PUT)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "ID do Veículo",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Dados completos do Veículo para atualização",
                        "name": "veiculo",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.Veiculo"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Veículo atualizado com sucesso",
                        "schema": {
                            "$ref": "#/definitions/models.Veiculo"
                        }
                    },
                    "400": {
                        "description": "error\": \"ID da empresa inválido\" or \"error\": \"ID do veículo inválido\" or \"error\": \"Dados inválidos: {detalhe_do_erro}",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "error\": \"ID da empresa não encontrado no token",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "error\": \"Veículo não encontrado ou não pertence à empresa",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao atualizar veículo",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Realiza a exclusão lógica de um veículo (define o campo 'ativo' como false), dado seu ID.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Veículos"
                ],
                "summary": "Excluir Veículo (Lógico)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "ID do Veículo",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "message\": \"Veículo excluído com sucesso",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "error\": \"ID da empresa inválido\" or \"error\": \"ID do veículo inválido",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "error\": \"ID da empresa não encontrado no token",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "error\": \"Veículo não encontrado ou não pertence à empresa",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao excluir veículo",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            },
            "patch": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Atualiza parcialmente os dados de um veículo existente, dado seu ID. Apenas os campos fornecidos na requisição serão alterados.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Veículos"
                ],
                "summary": "Atualizar Parcialmente Veículo (PATCH)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "ID do Veículo",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Campos do Veículo para atualização parcial. Datas devem ser strings no formato RFC3339 (YYYY-MM-DDTHH:MM:SSZ) ou YYYY-MM-DD.",
                        "name": "veiculo_update",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Veículo atualizado com sucesso",
                        "schema": {
                            "$ref": "#/definitions/models.Veiculo"
                        }
                    },
                    "400": {
                        "description": "error\": \"ID da empresa inválido\" or \"error\": \"ID do veículo inválido\" or \"error\": \"Dados inválidos\" or \"error\": \"Nenhum campo válido para atualização fornecido...\" or \"error\": \"Formato de data inválido...",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "error\": \"ID da empresa não encontrado no token",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "error\": \"Veículo não encontrado ou não pertence à empresa",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "error\": \"Erro ao atualizar veículo",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "handlers.CompletarCadastroRequest": {
            "type": "object",
            "required": [
                "email_key",
                "name",
                "password"
            ],
            "properties": {
                "email_key": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "password": {
                    "description": "Exige senha com no mínimo 6 caracteres",
                    "type": "string",
                    "minLength": 6
                }
            }
        },
        "handlers.CompletarResetSenhaRequest": {
            "type": "object",
            "required": [
                "new_password",
                "reset_key"
            ],
            "properties": {
                "new_password": {
                    "description": "Exige senha com no mínimo 6 caracteres",
                    "type": "string",
                    "minLength": 6
                },
                "reset_key": {
                    "type": "string"
                }
            }
        },
        "handlers.IniciarCadastroRequest": {
            "type": "object",
            "required": [
                "email"
            ],
            "properties": {
                "email": {
                    "type": "string"
                }
            }
        },
        "handlers.IniciarResetSenhaRequest": {
            "type": "object",
            "required": [
                "email"
            ],
            "properties": {
                "email": {
                    "type": "string"
                }
            }
        },
        "handlers.LoginRequest": {
            "type": "object",
            "required": [
                "email",
                "password"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                }
            }
        },
        "handlers.RefreshTokenRequest": {
            "type": "object",
            "required": [
                "refresh_token"
            ],
            "properties": {
                "refresh_token": {
                    "type": "string"
                }
            }
        },
        "handlers.ValidarCadastroRequest": {
            "type": "object",
            "required": [
                "email_key"
            ],
            "properties": {
                "email_key": {
                    "type": "string"
                }
            }
        },
        "handlers.ValidarResetSenhaRequest": {
            "type": "object",
            "required": [
                "reset_key"
            ],
            "properties": {
                "reset_key": {
                    "type": "string"
                }
            }
        },
        "handlers.VerifiedMotoristaData": {
            "type": "object",
            "properties": {
                "activated_at": {
                    "description": "Vem de activatedAt (Firestore)",
                    "type": "string"
                },
                "active": {
                    "description": "Derivado de status == \"active\" (Firestore) -\u003e models.Motorista.Ativo",
                    "type": "boolean"
                },
                "app_status": {
                    "description": "Vem de status (Firestore) -\u003e models.Motorista.AppStatus",
                    "type": "string"
                },
                "cnh_category": {
                    "description": "models.Motorista.CNHCategoria",
                    "type": "string"
                },
                "cnh_expiration": {
                    "description": "\"DD/MM/YYYY\" do Firestore (cnhValidade)",
                    "type": "string"
                },
                "cpf": {
                    "description": "models.Motorista.CPF",
                    "type": "string"
                },
                "email": {
                    "description": "models.Motorista.Email",
                    "type": "string"
                },
                "firebase_auth_uid": {
                    "description": "Vem de uid (Auth UID do Firestore)",
                    "type": "string"
                },
                "firebase_email": {
                    "description": "Campos do Firestore que podem ser úteis para referência ou para adicionar ao models.Motorista",
                    "type": "string"
                },
                "firestore_created_at": {
                    "description": "Vem de createdAt (Firestore)",
                    "type": "string"
                },
                "firestore_updated_at": {
                    "description": "Vem de updatedAt (Firestore)",
                    "type": "string"
                },
                "firestore_user_id": {
                    "description": "Vem de userId (Firestore doc ID) -\u003e models.Motorista.FirestoreUserID",
                    "type": "string"
                },
                "modules": {
                    "description": "models.Motorista.Modulos",
                    "type": "object",
                    "additionalProperties": {
                        "type": "boolean"
                    }
                },
                "name": {
                    "description": "Campos principais para pré-preenchimento do formulário de criação do motorista",
                    "type": "string"
                },
                "phone": {
                    "description": "models.Motorista.Telefone",
                    "type": "string"
                },
                "photo_url": {
                    "description": "Vem de avatarURL (Firestore) -\u003e models.Motorista.Foto.URL (ou campo similar)",
                    "type": "string"
                },
                "type_user": {
                    "description": "Vem de typeUser (Firestore) -\u003e models.Motorista.TipoUsuario",
                    "type": "string"
                },
                "verification_code": {
                    "description": "models.Motorista.CodigoVerificacao",
                    "type": "string"
                }
            }
        },
        "models.Anexo": {
            "type": "object",
            "properties": {
                "data_upload": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "models.FotoMetadata": {
            "type": "object",
            "properties": {
                "content_type": {
                    "description": "Ex: image/jpeg",
                    "type": "string"
                },
                "extension": {
                    "description": "Ex: .jpg, .png",
                    "type": "string"
                },
                "file_id": {
                    "description": "ID usado para nomear o arquivo base",
                    "type": "string"
                },
                "url": {
                    "description": "URL completa, agora persistida",
                    "type": "string"
                }
            }
        },
        "models.Motorista": {
            "type": "object",
            "properties": {
                "activated_at": {
                    "description": "Novos campos do Firestore para sincronização",
                    "type": "string"
                },
                "active": {
                    "description": "Mapeado de 'active' no JSON",
                    "type": "boolean"
                },
                "appStatus": {
                    "description": "e.g., \"pending\"",
                    "type": "string"
                },
                "attachments": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Anexo"
                    }
                },
                "birthDate": {
                    "description": "'birthDate' no JSON",
                    "type": "string"
                },
                "bloodType": {
                    "type": "string"
                },
                "cep": {
                    "description": "Campos de Endereco (achatados)",
                    "type": "string"
                },
                "city": {
                    "type": "string"
                },
                "cnh": {
                    "description": "Campos de CNH (achatados)",
                    "type": "string"
                },
                "cnhCategory": {
                    "type": "string"
                },
                "cnhExpiration": {
                    "type": "string"
                },
                "cnhIssuanceDate": {
                    "type": "string"
                },
                "cnhState": {
                    "type": "string"
                },
                "complement": {
                    "type": "string"
                },
                "config": {
                    "description": "Campos de configuração e permissões Firestore",
                    "type": "object",
                    "additionalProperties": {
                        "type": "boolean"
                    }
                },
                "cpf": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "emergencyContact": {
                    "type": "string"
                },
                "emergencyPhone": {
                    "type": "string"
                },
                "empresa": {
                    "description": "Já existe, será preenchido pelo token",
                    "type": "string"
                },
                "firebase_auth_uid": {
                    "type": "string"
                },
                "firebase_email": {
                    "type": "string"
                },
                "firestore_user_id": {
                    "description": "ID do documento do usuário no Firestore",
                    "type": "string"
                },
                "gender": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "modules": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "boolean"
                    }
                },
                "name": {
                    "description": "Campos específicos do motorista do JSON",
                    "type": "string"
                },
                "neighborhood": {
                    "type": "string"
                },
                "number": {
                    "type": "string"
                },
                "observations": {
                    "type": "string"
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "phone": {
                    "description": "'phone' no JSON",
                    "type": "string"
                },
                "photo": {
                    "$ref": "#/definitions/models.FotoMetadata"
                },
                "rg": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                },
                "street": {
                    "type": "string"
                },
                "timezone": {
                    "type": "string"
                },
                "timezoneOffset": {
                    "type": "integer"
                },
                "typeUser": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "verificationCode": {
                    "type": "string"
                }
            }
        },
        "models.Veiculo": {
            "type": "object",
            "properties": {
                "active": {
                    "type": "boolean"
                },
                "brand": {
                    "type": "string"
                },
                "chassisNumber": {
                    "type": "string"
                },
                "color": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "documents": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Anexo"
                    }
                },
                "empresa": {
                    "description": "Associado à empresa do token",
                    "type": "string"
                },
                "fuelType": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "insuranceCompany": {
                    "type": "string"
                },
                "insuranceExpirationDate": {
                    "description": "Usando ponteiro para permitir nulo",
                    "type": "string"
                },
                "insurancePolicy": {
                    "type": "string"
                },
                "mileage": {
                    "type": "integer"
                },
                "model": {
                    "type": "string"
                },
                "modelYear": {
                    "type": "string"
                },
                "nickname": {
                    "type": "string"
                },
                "notes": {
                    "type": "string"
                },
                "observations": {
                    "type": "string"
                },
                "photo": {
                    "$ref": "#/definitions/models.FotoMetadata"
                },
                "plate": {
                    "type": "string"
                },
                "qrCode": {
                    "type": "string"
                },
                "renavam": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "tankCapacity": {
                    "type": "integer"
                },
                "type": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "year": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "description": "Type \"Bearer\" followed by a space and JWT token.",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "wedrivergoapi.srv.weso.com.br",
	BasePath:         "/api/v1",
	Schemes:          []string{"https", "http"},
	Title:            "WedriverApiGO API",
	Description:      "API para gerenciamento e operações da Wedriver.",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
