# Arquivos de configuração com credenciais
config.txt

# Arquivos de compilação
*.exe
*.exe~
*.dll
*.so
*.dylib

# Arquivos temporários
*.tmp
*.bak
*.log

# Diretórios de dependências e compilação
/vendor/
/bin/
/pkg/

# Arquivos específicos do sistema operacional
.DS_Store
Thumbs.db

# Arquivos específicos do IDE
.idea/
.vscode/
*.swp
*.swo

# Added by Claude Task Master
# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json

# Firebase credentials
data/

# Environment variables file
.env
tasks/ 
build/linux/wedriverapigo
