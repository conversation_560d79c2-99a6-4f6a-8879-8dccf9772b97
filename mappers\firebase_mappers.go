package mappers

import (
	"strings"
	"time"
	"wedriverapigo/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// FirestoreFieldMap define o mapeamento entre campos do MongoDB e Firestore
type FirestoreFieldMap struct {
	MongoField     string
	FirestoreField string
	Description    string
}

// Mapeamentos padrão entre MongoDB e Firestore para Motorista
var MotoristaToFirestoreMap = []FirestoreFieldMap{
	{MongoField: "_id", FirestoreField: "userMID", Description: "ID do MongoDB"},
	{MongoField: "nome", FirestoreField: "fullName", Description: "Nome completo do motorista"},
	{MongoField: "email", FirestoreField: "email", Description: "Email do motorista"},
	{MongoField: "telefone", FirestoreField: "phone", Description: "Telefone do motorista"},
	{MongoField: "ativo", FirestoreField: "active", Description: "Status ativo do motorista"},
	{MongoField: "app_status", FirestoreField: "status", Description: "Status do aplicativo"},
	{MongoField: "data_nasc", FirestoreField: "birthDate", Description: "Data de nascimento"},
	{MongoField: "foto.url", FirestoreField: "avatarURL", Description: "URL da foto do perfil"},
	{MongoField: "tipo_usuario", FirestoreField: "typeUser", Description: "Tipo de usuário"},
	{MongoField: "codigo_verificacao", FirestoreField: "verificationCode", Description: "Código de verificação"},
	{MongoField: "modulos", FirestoreField: "modules", Description: "Módulos do motorista"},
	{MongoField: "config", FirestoreField: "config", Description: "Configurações do motorista"},
	{MongoField: "permissions", FirestoreField: "permissions", Description: "Permissões do motorista"},
	{MongoField: "timezone", FirestoreField: "timezone", Description: "Fuso horário do motorista"},
	{MongoField: "timezone_offset", FirestoreField: "timezoneOffset", Description: "Deslocamento do fuso horário"},
	{MongoField: "cnh_categoria", FirestoreField: "cnhCategory", Description: "Categoria da CNH"},
	{MongoField: "cnh_validade", FirestoreField: "cnhExpiration", Description: "Validade da CNH"},
	{MongoField: "empresa", FirestoreField: "companyId", Description: "ID da empresa"},
	{MongoField: "created_at", FirestoreField: "createdAt", Description: "Data de criação"},
	{MongoField: "updated_at", FirestoreField: "updatedAt", Description: "Data de atualização"},
	{MongoField: "activated_at", FirestoreField: "activatedAt", Description: "Data de ativação"},
	{MongoField: "firebase_email", FirestoreField: "firebaseEmail", Description: "Email do Firebase"},
	{MongoField: "firebase_auth_uid", FirestoreField: "uid", Description: "UID do Firebase Auth"},
}

// MongoToFirestore converte um motorista do MongoDB para um mapa do Firestore
func MongoToFirestore(motorista *models.Motorista, firebaseAuthEmail string, firebaseAuthUID string, firestoreDocID string) map[string]interface{} {
	firestoreData := map[string]interface{}{
		"userMID":   motorista.ID.Hex(),
		"userId":    firestoreDocID,
		"fullName":  motorista.Nome,
		"email":     motorista.Email,
		"phone":     motorista.Telefone,
		"typeUser":  "driver",
		"companyId": motorista.Empresa.Hex(),
		"updatedAt": time.Now().UTC(),
		"active":    motorista.Ativo,
		"cpf":       motorista.CPF,
	}

	// Campos condicionais
	if motorista.Foto.URL != "" {
		firestoreData["avatarURL"] = motorista.Foto.URL
	}

	// Status
	if motorista.AppStatus != "" {
		firestoreData["status"] = motorista.AppStatus
	} else {
		firestoreData["status"] = "create_password" // Status padrão para novos usuários
	}

	// Firebase Auth
	if firebaseAuthEmail != "" {
		firestoreData["firebaseEmail"] = firebaseAuthEmail
		firestoreData["hasPassword"] = true
	}

	if firebaseAuthUID != "" {
		firestoreData["uid"] = firebaseAuthUID
	}

	// Campos opcionais
	if motorista.CodigoVerificacao != "" {
		firestoreData["verificationCode"] = motorista.CodigoVerificacao
	}

	if motorista.ActivatedAt != nil && !motorista.ActivatedAt.IsZero() {
		firestoreData["activatedAt"] = *motorista.ActivatedAt
	}

	if motorista.CNHCategoria != "" {
		firestoreData["cnhCategory"] = motorista.CNHCategoria
	}

	if !motorista.CNHValidade.IsZero() {
		firestoreData["cnhExpiration"] = motorista.CNHValidade.Format("02/01/2006")
	}

	// Módulos
	if motorista.Modulos != nil {
		firestoreData["modules"] = motorista.Modulos
	} else {
		firestoreData["modules"] = make(map[string]bool)
	}

	// Config
	if motorista.Config != nil {
		firestoreData["config"] = motorista.Config
	} else {
		firestoreData["config"] = make(map[string]bool)
	}

	// Permissions
	if motorista.Permissions != nil {
		firestoreData["permissions"] = motorista.Permissions
	} else {
		firestoreData["permissions"] = make([]string, 0)
	}

	firestoreData["timezone"] = motorista.Timezone
	firestoreData["timezoneOffset"] = motorista.TimezoneOffset

	return firestoreData
}

// CreateFirestoreUser cria um mapa simplificado para a criação inicial de um usuário no Firestore
func CreateFirestoreUser(motorista *models.Motorista, avatarURL string, firestoreDocID string) map[string]interface{} {
	firestoreData := map[string]interface{}{
		"cpf":       motorista.CPF,
		"email":     motorista.Email,
		"fullName":  motorista.Nome, // Usando fullName em vez de name para consistência com o schema
		"typeUser":  "driver",       // Usando typeUser em vez de userType para consistência com o schema
		"companyId": motorista.Empresa.Hex(),
		"active":    motorista.Ativo,
		"createdAt": time.Now().UTC(),
		"updatedAt": time.Now().UTC(),
		"userMID":   motorista.ID.Hex(),
		"userId":    firestoreDocID,
	}

	if avatarURL != "" {
		firestoreData["avatarURL"] = avatarURL
	}

	if motorista.Telefone != "" {
		firestoreData["phone"] = motorista.Telefone
	}

	return firestoreData
}

// UpdateSpecificFields cria um mapa para atualização parcial de campos no Firestore
func UpdateSpecificFields(motorista models.Motorista, fieldsToUpdate []string) map[string]interface{} {
	result := map[string]interface{}{
		"updatedAt": time.Now().UTC(),
	}

	for _, field := range fieldsToUpdate {
		switch field {
		case "nome":
			result["fullName"] = motorista.Nome
		case "email":
			result["email"] = strings.ToLower(motorista.Email)
		case "telefone":
			result["phone"] = motorista.Telefone
		case "app_status":
			result["status"] = motorista.AppStatus
		case "ativo":
			if motorista.Ativo {
				result["status"] = "active"
			} else {
				result["status"] = "inactive"
			}
			result["active"] = motorista.Ativo
		case "data_nasc":
			if !motorista.DataNasc.IsZero() {
				result["birthDate"] = motorista.DataNasc
			}
		case "foto":
			result["avatarURL"] = motorista.Foto.URL
		case "tipo_usuario":
			if motorista.TipoUsuario != "" {
				result["typeUser"] = motorista.TipoUsuario
			} else {
				result["typeUser"] = "driver"
			}
		}
	}

	return result
}

// FirestoreToMotorista converte dados do Firestore para um modelo de Motorista
func FirestoreToMotorista(firestoreID string, firestoreData map[string]interface{}) *models.Motorista {
	motorista := &models.Motorista{
		FirestoreUserID: firestoreID,
	}

	// Preencher campos básicos
	if val, ok := firestoreData["cpf"].(string); ok {
		motorista.CPF = val
	}

	if val, ok := firestoreData["fullName"].(string); ok {
		motorista.Nome = val
	} else if val, ok := firestoreData["name"].(string); ok {
		motorista.Nome = val
	}

	if val, ok := firestoreData["email"].(string); ok {
		motorista.Email = val
	}

	if val, ok := firestoreData["phone"].(string); ok {
		motorista.Telefone = val
	}

	if val, ok := firestoreData["status"].(string); ok {
		motorista.AppStatus = val
	}

	if val, ok := firestoreData["active"].(bool); ok {
		motorista.Ativo = val
	} else {
		// Se não especificado, definir como true por padrão
		motorista.Ativo = true
	}

	if val, ok := firestoreData["typeUser"].(string); ok {
		motorista.TipoUsuario = val
	}

	if val, ok := firestoreData["verificationCode"].(string); ok {
		motorista.CodigoVerificacao = val
	}

	// Campos de data
	if val, ok := firestoreData["birthDate"].(time.Time); ok {
		motorista.DataNasc = &val
	}

	if val, ok := firestoreData["activatedAt"].(time.Time); ok {
		motorista.ActivatedAt = &val
	}

	// Foto
	if val, ok := firestoreData["avatarURL"].(string); ok && val != "" {
		motorista.Foto.URL = val
	}

	// Campos do Firebase Auth
	if val, ok := firestoreData["firebaseEmail"].(string); ok {
		temp := val
		motorista.FirebaseEmail = &temp
	}

	if val, ok := firestoreData["uid"].(string); ok {
		temp := val
		motorista.FirebaseAuthUID = &temp
	}

	// Campos de configuração e permissões
	if val, ok := firestoreData["config"].(map[string]interface{}); ok {
		config := make(map[string]bool)
		for k, v := range val {
			if boolVal, ok := v.(bool); ok {
				config[k] = boolVal
			}
		}
		motorista.Config = config
	}

	if val, ok := firestoreData["permissions"].([]interface{}); ok {
		permissions := make([]string, 0, len(val))
		for _, v := range val {
			if strVal, ok := v.(string); ok {
				permissions = append(permissions, strVal)
			}
		}
		motorista.Permissions = permissions
	}

	if val, ok := firestoreData["modules"].(map[string]interface{}); ok {
		modules := make(map[string]bool)
		for k, v := range val {
			if boolVal, ok := v.(bool); ok {
				modules[k] = boolVal
			}
		}
		motorista.Modulos = modules
	}

	if val, ok := firestoreData["timezone"].(string); ok {
		motorista.Timezone = val
	}

	if val, ok := firestoreData["timezoneOffset"].(int); ok {
		motorista.TimezoneOffset = val
	} else if val, ok := firestoreData["timezoneOffset"].(float64); ok {
		motorista.TimezoneOffset = int(val)
	}

	// Tentar converter userMID para ObjectID se existir (mas não definir como ID principal)
	// O ID principal será gerado no endpoint AtivarMotorista
	if val, ok := firestoreData["userMID"].(string); ok && val != "" {
		if objID, err := primitive.ObjectIDFromHex(val); err == nil {
			// Se já existe um userMID válido, pode indicar que já foi ativado
			motorista.ID = objID
		}
	}

	return motorista
}
