package handlers

import (
	"time"
	"wedriverapigo/mappers"
)

// VerifiedMotoristaData é a struct de resposta para o endpoint GET /motorista/verify-code.
// Contém dados do Firestore pré-formatados para o frontend.
type VerifiedMotoristaData struct {
	// Campos principais para pré-preenchimento do formulário de criação do motorista
	Nome              string          `json:"name,omitempty"`              // Vem de fullName (Firestore) -> models.Motorista.Nome
	CPF               string          `json:"cpf,omitempty"`               // models.Motorista.CPF
	Email             string          `json:"email,omitempty"`             // models.Motorista.Email
	Telefone          string          `json:"phone,omitempty"`             // models.Motorista.Telefone
	AppStatus         string          `json:"app_status,omitempty"`        // Vem de status (Firestore) -> models.Motorista.AppStatus
	Active            bool            `json:"active"`                      // Derivado de status == "active" (Firestore) -> models.Motorista.Ativo
	TipoUsuario       string          `json:"type_user,omitempty"`         // Vem de typeUser (Firestore) -> models.Motorista.TipoUsuario
	CodigoVerificacao string          `json:"verification_code,omitempty"` // models.Motorista.CodigoVerificacao
	FirestoreUserID   string          `json:"firestore_user_id,omitempty"` // Vem de userId (Firestore doc ID) -> models.Motorista.FirestoreUserID
	FotoURL           string          `json:"photo_url,omitempty"`         // Vem de avatarURL (Firestore) -> models.Motorista.Foto.URL (ou campo similar)
	CNHCategoria      string          `json:"cnh_category,omitempty"`      // models.Motorista.CNHCategoria
	CNHValidadeString string          `json:"cnh_expiration,omitempty"`    // "DD/MM/YYYY" do Firestore (cnhValidade)
	Modulos           map[string]bool `json:"modules,omitempty"`           // models.Motorista.Modulos

	// Campos do Firestore que podem ser úteis para referência ou para adicionar ao models.Motorista
	FirebaseEmail      *string    `json:"firebase_email,omitempty"`       // Vem de firebaseEmail (Firestore)
	FirebaseAuthUID    *string    `json:"firebase_auth_uid,omitempty"`    // Vem de uid (Auth UID do Firestore)
	ActivatedAt        *time.Time `json:"activated_at,omitempty"`         // Vem de activatedAt (Firestore)
	FirestoreCreatedAt *time.Time `json:"firestore_created_at,omitempty"` // Vem de createdAt (Firestore)
	FirestoreUpdatedAt *time.Time `json:"firestore_updated_at,omitempty"` // Vem de updatedAt (Firestore)
}

// MapFirestoreToVerifiedMotorista converte os dados brutos do Firestore para a struct VerifiedMotoristaData.
func MapFirestoreToVerifiedMotorista(docID string, firestoreData map[string]interface{}) VerifiedMotoristaData {
	// Usar o novo mapeador centralizado para converter dados do Firestore para Motorista
	motorista := mappers.FirestoreToMotorista(docID, firestoreData)

	// Converter para VerifiedMotoristaData
	responseData := VerifiedMotoristaData{
		FirestoreUserID:   docID,
		CPF:               motorista.CPF,
		Nome:              motorista.Nome,
		Email:             motorista.Email,
		Telefone:          motorista.Telefone,
		AppStatus:         motorista.AppStatus,
		Active:            motorista.Ativo,
		TipoUsuario:       motorista.TipoUsuario,
		CodigoVerificacao: motorista.CodigoVerificacao,
		FotoURL:           motorista.Foto.URL,
		CNHCategoria:      motorista.CNHCategoria,
		FirebaseEmail:     motorista.FirebaseEmail,
		FirebaseAuthUID:   motorista.FirebaseAuthUID,
		ActivatedAt:       motorista.ActivatedAt,
	}

	// Campos específicos que precisam de tratamento adicional
	if val, ok := firestoreData["cnhExpiration"].(string); ok {
		responseData.CNHValidadeString = val
	}

	if modulesInterface, ok := firestoreData["modules"].(map[string]interface{}); ok {
		responseData.Modulos = make(map[string]bool)
		for k, v := range modulesInterface {
			if boolVal, isBool := v.(bool); isBool {
				responseData.Modulos[k] = boolVal
			}
		}
	}

	if val, ok := firestoreData["createdAt"].(time.Time); ok {
		responseData.FirestoreCreatedAt = &val
	}

	if val, ok := firestoreData["updatedAt"].(time.Time); ok {
		responseData.FirestoreUpdatedAt = &val
	}

	return responseData
}
