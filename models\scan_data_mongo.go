package models

import "time"

// LocationData armazena informações de latitude e longitude.
type LocationData struct {
	Latitude  float64 `bson:"latitude,omitempty"`
	Longitude float64 `bson:"longitude,omitempty"`
}

// MotoristaLogData armazena informações do motorista para auditoria/log no momento da identificação.
// Este campo serve como um snapshot dos dados do motorista no momento da criação da identificação.
type MotoristaLogData struct {
	Nome string `bson:"nome,omitempty"` // Nome do motorista no momento da identificação
	CPF  string `bson:"cpf,omitempty"`  // CPF do motorista no momento da identificação
}

// ScanDataMongoDB representa a estrutura dos dados de 'scans' como serão armazenados no MongoDB.
// Esta struct será usada para a coleção 'identificacoes'.
type ScanDataMongoDB struct {
	ID           string            `bson:"_id"` // ID do documento Firestore usado como _id no MongoDB
	CreatedAt    time.Time         `bson:"createdAt,omitempty"`
	SyncedAt     *time.Time        `bson:"syncedAt,omitempty"`
	Descricao    string            `bson:"descricao,omitempty"`
	OriginalId   string            `bson:"originalId,omitempty"` // Campo 'id' original do Firestore
	Location     *LocationData     `bson:"location,omitempty"`
	Mac          string            `bson:"mac,omitempty"`
	Modelo       string            `bson:"modelo,omitempty"`
	Placa        string            `bson:"placa,omitempty"`
	Processed    bool              `bson:"processed"` // Estado 'processed' conforme recebido (deve ser false)
	Type         string            `bson:"type,omitempty"`
	TypeScan     string            `bson:"typeScan,omitempty"`
	UpdatedAt    *time.Time        `bson:"updatedAt,omitempty"` // Opcional, pode não estar presente no doc original
	UserId       string            `bson:"userMID,omitempty"`
	VehicleId    string            `bson:"vehicleId,omitempty"`     // ID do veículo no MongoDB (seguindo padrão do userMID)
	MotoristaLog *MotoristaLogData `bson:"motorista_log,omitempty"` // Dados do motorista para auditoria/log
}
