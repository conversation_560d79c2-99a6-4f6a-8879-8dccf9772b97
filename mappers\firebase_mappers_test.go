package mappers

import (
	"testing"
	"wedriverapigo/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TestMongoToFirestore(t *testing.T) {
	// Criar um ID de teste
	id, _ := primitive.ObjectIDFromHex("5f50c31f5b5c8a1c9c7b3b3a")
	empresaID, _ := primitive.ObjectIDFromHex("5f50c31f5b5c8a1c9c7b3b3b")

	// Criar um motorista de teste
	motorista := &models.Motorista{
		ID:                id,
		Empresa:           empresaID,
		Nome:              "Teste da Silva",
		CPF:               "12345678900",
		Email:             "<EMAIL>",
		Telefone:          "11999999999",
		Ativo:             true,
		AppStatus:         "active",
		TipoUsuario:       "driver",
		CodigoVerificacao: "123456",
	}

	motorista.Foto.URL = "https://example.com/foto.jpg"

	// Testar conversão para Firestore
	firebaseAuthEmail := "<EMAIL>"
	firebaseAuthUID := "firebase-uid-123"
	firestoreDocID := "firestore-doc-123"

	result := MongoToFirestore(motorista, firebaseAuthEmail, firebaseAuthUID, firestoreDocID)

	// Verificar campos obrigatórios
	if result["userMID"] != id.Hex() {
		t.Errorf("Campo userMId incorreto. Esperado: %s, Obtido: %s", id.Hex(), result["userMID"])
	}

	if result["fullName"] != "Teste da Silva" {
		t.Errorf("Campo fullName incorreto. Esperado: %s, Obtido: %s", "Teste da Silva", result["fullName"])
	}

	if result["cpf"] != "12345678900" {
		t.Errorf("Campo cpf incorreto. Esperado: %s, Obtido: %s", "12345678900", result["cpf"])
	}

	if result["email"] != "<EMAIL>" {
		t.Errorf("Campo email incorreto. Esperado: %s, Obtido: %s", "<EMAIL>", result["email"])
	}

	if result["firebaseEmail"] != firebaseAuthEmail {
		t.Errorf("Campo firebaseEmail incorreto. Esperado: %s, Obtido: %s", firebaseAuthEmail, result["firebaseEmail"])
	}

	if result["uid"] != firebaseAuthUID {
		t.Errorf("Campo uid incorreto. Esperado: %s, Obtido: %s", firebaseAuthUID, result["uid"])
	}

	if result["avatarURL"] != "https://example.com/foto.jpg" {
		t.Errorf("Campo avatarURL incorreto. Esperado: %s, Obtido: %s", "https://example.com/foto.jpg", result["avatarURL"])
	}

	if result["userId"] != firestoreDocID {
		t.Errorf("Campo userId incorreto. Esperado: %s, Obtido: %s", firestoreDocID, result["userId"])
	}
}

func TestFirestoreToMotorista(t *testing.T) {
	// Criar dados de teste do Firestore
	firestoreID := "firestore-doc-123"
	firestoreData := map[string]interface{}{
		"cpf":              "12345678900",
		"fullName":         "Teste da Silva",
		"email":            "<EMAIL>",
		"phone":            "11999999999",
		"status":           "active",
		"typeUser":         "driver",
		"avatarURL":        "https://example.com/foto.jpg",
		"userMID":           "5f50c31f5b5c8a1c9c7b3b3a",
		"verificationCode": "123456",
	}

	// Testar conversão para Motorista
	result := FirestoreToMotorista(firestoreID, firestoreData)

	// Verificar campos obrigatórios
	if result.FirestoreUserID != firestoreID {
		t.Errorf("Campo FirestoreUserID incorreto. Esperado: %s, Obtido: %s", firestoreID, result.FirestoreUserID)
	}

	if result.CPF != "12345678900" {
		t.Errorf("Campo CPF incorreto. Esperado: %s, Obtido: %s", "12345678900", result.CPF)
	}

	if result.Nome != "Teste da Silva" {
		t.Errorf("Campo Nome incorreto. Esperado: %s, Obtido: %s", "Teste da Silva", result.Nome)
	}

	if result.Email != "<EMAIL>" {
		t.Errorf("Campo Email incorreto. Esperado: %s, Obtido: %s", "<EMAIL>", result.Email)
	}

	if result.Telefone != "11999999999" {
		t.Errorf("Campo Telefone incorreto. Esperado: %s, Obtido: %s", "11999999999", result.Telefone)
	}

	if result.AppStatus != "active" {
		t.Errorf("Campo AppStatus incorreto. Esperado: %s, Obtido: %s", "active", result.AppStatus)
	}

	if result.TipoUsuario != "driver" {
		t.Errorf("Campo TipoUsuario incorreto. Esperado: %s, Obtido: %s", "driver", result.TipoUsuario)
	}

	if result.Foto.URL != "https://example.com/foto.jpg" {
		t.Errorf("Campo Foto.URL incorreto. Esperado: %s, Obtido: %s", "https://example.com/foto.jpg", result.Foto.URL)
	}

	expectedID, _ := primitive.ObjectIDFromHex("5f50c31f5b5c8a1c9c7b3b3a")
	if result.ID != expectedID {
		t.Errorf("Campo ID incorreto. Esperado: %s, Obtido: %s", expectedID.Hex(), result.ID.Hex())
	}
}

func TestUpdateSpecificFields(t *testing.T) {
	// Criar um motorista de teste
	motorista := models.Motorista{
		Nome:        "Teste da Silva",
		Email:       "<EMAIL>",
		Telefone:    "11999999999",
		Ativo:       true,
		AppStatus:   "active",
		TipoUsuario: "driver",
	}
	motorista.Foto.URL = "https://example.com/foto.jpg"

	// Testar atualização de campos específicos
	fieldsToUpdate := []string{"nome", "email", "foto"}
	result := UpdateSpecificFields(motorista, fieldsToUpdate)

	// Verificar campos atualizados
	if result["fullName"] != "Teste da Silva" {
		t.Errorf("Campo fullName incorreto. Esperado: %s, Obtido: %s", "Teste da Silva", result["fullName"])
	}

	if result["email"] != "<EMAIL>" {
		t.Errorf("Campo email incorreto. Esperado: %s, Obtido: %s", "<EMAIL>", result["email"])
	}

	if result["avatarURL"] != "https://example.com/foto.jpg" {
		t.Errorf("Campo avatarURL incorreto. Esperado: %s, Obtido: %s", "https://example.com/foto.jpg", result["avatarURL"])
	}

	// Verificar que o campo updatedAt está presente
	if _, ok := result["updatedAt"]; !ok {
		t.Errorf("Campo updatedAt não encontrado")
	}

	// Verificar que outros campos não estão presentes
	if _, ok := result["phone"]; ok {
		t.Errorf("Campo phone não deveria estar presente")
	}
}

func TestCreateFirestoreUser(t *testing.T) {
	// Criar um ID de teste
	id, _ := primitive.ObjectIDFromHex("5f50c31f5b5c8a1c9c7b3b3a")
	empresaID, _ := primitive.ObjectIDFromHex("5f50c31f5b5c8a1c9c7b3b3b")

	// Criar um motorista de teste
	motorista := &models.Motorista{
		ID:       id,
		Empresa:  empresaID,
		Nome:     "Teste da Silva",
		CPF:      "12345678900",
		Email:    "<EMAIL>",
		Telefone: "11999999999",
		Ativo:    true,
	}

	// Testar criação de usuário no Firestore
	avatarURL := "https://example.com/foto.jpg"
	firestoreDocID := "firestore-doc-123"
	result := CreateFirestoreUser(motorista, avatarURL, firestoreDocID)

	// Verificar campos obrigatórios
	if result["userMID"] != id.Hex() {
		t.Errorf("Campo userMId incorreto. Esperado: %s, Obtido: %s", id.Hex(), result["userMID"])
	}

	if result["name"] != "Teste da Silva" {
		t.Errorf("Campo name incorreto. Esperado: %s, Obtido: %s", "Teste da Silva", result["name"])
	}

	if result["cpf"] != "12345678900" {
		t.Errorf("Campo cpf incorreto. Esperado: %s, Obtido: %s", "12345678900", result["cpf"])
	}

	if result["email"] != "<EMAIL>" {
		t.Errorf("Campo email incorreto. Esperado: %s, Obtido: %s", "<EMAIL>", result["email"])
	}

	if result["avatarURL"] != "https://example.com/foto.jpg" {
		t.Errorf("Campo avatarURL incorreto. Esperado: %s, Obtido: %s", "https://example.com/foto.jpg", result["avatarURL"])
	}

	if result["phone"] != "11999999999" {
		t.Errorf("Campo phone incorreto. Esperado: %s, Obtido: %s", "11999999999", result["phone"])
	}

	if result["userId"] != firestoreDocID {
		t.Errorf("Campo userId incorreto. Esperado: %s, Obtido: %s", firestoreDocID, result["userId"])
	}
}
