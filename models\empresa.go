package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Endereco struct {
	Logradouro  string `bson:"logradouro,omitempty" json:"street,omitempty"`
	Numero      string `bson:"numero,omitempty" json:"number,omitempty"`
	Complemento string `bson:"complemento,omitempty" json:"complement,omitempty"`
	Bairro      string `bson:"bairro,omitempty" json:"neighborhood,omitempty"`
	Cidade      string `bson:"cidade,omitempty" json:"city,omitempty"`
	Estado      string `bson:"estado,omitempty" json:"state,omitempty"`
	CEP         string `bson:"cep,omitempty" json:"cep,omitempty"`
}

type Responsavel struct {
	Nome     string `bson:"nome,omitempty"`
	CPF      string `bson:"cpf,omitempty"`
	Telefone string `bson:"telefone,omitempty"`
}

type Empresa struct {
	ID           primitive.ObjectID `bson:"_id,omitempty"`
	Nome         string             `bson:"nome"` // Nome inicial pode ser derivado ou padrão
	CNPJ         string             `bson:"cnpj,omitempty"`
	Endereco     *Endereco          `bson:"endereco,omitempty"` // Usar ponteiro para permitir omitempty
	Telefone     string             `bson:"telefone,omitempty"`
	Email        string             `bson:"email,omitempty"` // Pode ser o mesmo do usuário inicial
	Ativo        bool               `bson:"ativo"`
	CPForCNPJ    string             `bson:"cpf_cnpj,omitempty"` // Campo unificado? Verificar schema
	Created      time.Time          `bson:"created"`
	EmailContato string             `bson:"email_contato,omitempty"`
	NomeFantasia string             `bson:"nome_fantasia,omitempty"`
	RazaoSocial  string             `bson:"razao_social,omitempty"`
	Updated      time.Time          `bson:"updated"`
	Observacoes  string             `bson:"observacoes,omitempty"`
	Responsavel  *Responsavel       `bson:"responsavel,omitempty"` // Usar ponteiro para permitir omitempty
	Segmento     string             `bson:"segmento,omitempty"`
	Website      string             `bson:"website,omitempty"`
}
