package utils

import (
	"crypto/rand"
	"math/big"
)

// GenerateRandomPassword cria uma senha aleatória segura com o comprimento especificado.
// A senha conterá letras maiúsculas, minúsculas e números.
func GenerateRandomPassword(length int) (string, error) {
	const letters = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
	ret := make([]byte, length)
	for i := 0; i < length; i++ {
		num, err := rand.Int(rand.Reader, big.NewInt(int64(len(letters))))
		if err != nil {
			return "", err
		}
		ret[i] = letters[num.Int64()]
	}
	return string(ret), nil
}
