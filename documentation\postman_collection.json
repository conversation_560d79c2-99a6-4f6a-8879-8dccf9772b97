{"info": {"_postman_id": "{{$guid}}", "name": "WedriverApiGO", "description": "Coleção Postman para a API WedriverApiGO", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Cadastro", "item": [{"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/cadastro/iniciar", "host": ["{{baseUrl}}"], "path": ["api", "v1", "cadastro", "iniciar"]}}, "response": []}, {"name": "Validar Cadastro", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"codigo\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/cadastro/validar", "host": ["{{baseUrl}}"], "path": ["api", "v1", "cadastro", "validar"]}}, "response": []}, {"name": "Completar Cadastro", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"codigo\": \"123456\",\n  \"user\": {\n    \"name\": \"Nome do Usuário\",\n    \"hash_pass\": \"senhaForte123\"\n  },\n  \"empresa\": {\n    \"nome\": \"Nome da Empresa\",\n    \"cnpj\": \"00.000.000/0001-00\",\n    \"razao_social\": \"Razão Social da Empresa LTDA\",\n    \"nome_fantasia\": \"Nome Fantasia da Empresa\",\n    \"email_contato\": \"<EMAIL>\",\n    \"telefone\": \"(11) 99999-8888\",\n    \"endereco\": {\n      \"logradouro\": \"Rua Exemplo\",\n      \"numero\": \"123\",\n      \"bairro\": \"Centro\",\n      \"cidade\": \"São Paulo\",\n      \"estado\": \"SP\",\n      \"cep\": \"01000-000\"\n    },\n    \"responsavel\": {\n      \"nome\": \"Nome do Responsável\",\n      \"cpf\": \"111.222.333-44\",\n      \"telefone\": \"(11) 77777-6666\"\n    }\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/cadastro/completar", "host": ["{{baseUrl}}"], "path": ["api", "v1", "cadastro", "completar"]}}, "response": []}]}, {"name": "Autenticação", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);", "pm.environment.set(\"authToken\", jsonData.token);", "pm.environment.set(\"refreshToken\", jsonData.refresh_token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"senha\": \"senhaForte123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "login"]}}, "response": []}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);", "pm.environment.set(\"authToken\", jsonData.token);", "pm.environment.set(\"refreshToken\", jsonData.refresh_token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refresh_token\": \"{{refreshToken}}\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/refresh", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "refresh"]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "item": [{"name": "Iniciar Redefinição de Senha", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/redefinir-senha/iniciar", "host": ["{{baseUrl}}"], "path": ["api", "v1", "redefinir-se<PERSON>a", "iniciar"]}}, "response": []}, {"name": "Validar Código de Redefinição", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"codigo\": \"654321\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/redefinir-senha/validar", "host": ["{{baseUrl}}"], "path": ["api", "v1", "redefinir-se<PERSON>a", "validar"]}}, "response": []}, {"name": "Completar Redefinição de Senha", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"codigo\": \"654321\",\n  \"nova_senha\": \"novaSenhaSuperForte456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/redefinir-senha/completar", "host": ["{{baseUrl}}"], "path": ["api", "v1", "redefinir-se<PERSON>a", "completar"]}}, "response": []}]}, {"name": "Motorista", "item": [{"name": "Criar Motorista", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"cpf\": \"123.456.789-00\",\n  \"birthDate\": \"1990-01-15T00:00:00Z\",\n  \"phone\": \"(11) 91234-5678\",\n  \"email\": \"<EMAIL>\",\n  \"cnh\": \"12345678901\",\n  \"cnhCategory\": \"AB\",\n  \"cnhExpiration\": \"2028-12-31T00:00:00Z\",\n  \"cep\": \"02000-000\",\n  \"street\": \"Avenida Principal\",\n  \"number\": \"1000\",\n  \"neighborhood\": \"Vila Nova\",\n  \"city\": \"São Paulo\",\n  \"state\": \"SP\",\n  \"active\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/motorista", "host": ["{{baseUrl}}"], "path": ["api", "v1", "motorista"]}}, "response": []}, {"name": "Listar Motoristas", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/motorista", "host": ["{{baseUrl}}"], "path": ["api", "v1", "motorista"]}}, "response": []}, {"name": "Obter Motorista por ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/motorista/:id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "motorista", ":id"], "variable": [{"key": "id", "value": "ID_DO_MOTORISTA"}]}}, "response": []}, {"name": "Atualizar Motorista (PUT)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"João Motorista Atualizado\",\n  \"cpf\": \"123.456.789-00\",\n  \"birthDate\": \"1990-01-15T00:00:00Z\",\n  \"phone\": \"(11) 98765-4321\",\n  \"email\": \"<EMAIL>\",\n  \"cnh\": \"12345678901\",\n  \"cnhCategory\": \"AB\",\n  \"cnhExpiration\": \"2029-12-31T00:00:00Z\",\n  \"cep\": \"02000-001\",\n  \"street\": \"Avenida Principal, Bloco A\",\n  \"number\": \"1001\",\n  \"neighborhood\": \"Vila Nova Sul\",\n  \"city\": \"São Paulo\",\n  \"state\": \"SP\",\n  \"active\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/motorista/:id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "motorista", ":id"], "variable": [{"key": "id", "value": "ID_DO_MOTORISTA"}]}}, "response": []}, {"name": "Atualizar Parcial Motorista (PATCH)", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"(11) 00000-1111\",\n  \"email\": \"<EMAIL>\",\n  \"active\": false\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/motorista/:id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "motorista", ":id"], "variable": [{"key": "id", "value": "ID_DO_MOTORISTA"}]}}, "response": []}, {"name": "Deletar Motorista", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/motorista/:id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "motorista", ":id"], "variable": [{"key": "id", "value": "ID_DO_MOTORISTA"}]}}, "response": []}]}, {"name": "Rota<PERSON> (Exemplo)", "item": [{"name": "Obter Informações do Usuário", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/protected/user-info", "host": ["{{baseUrl}}"], "path": ["api", "v1", "protected", "user-info"]}}, "response": []}, {"name": "Dashboard Admin", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/protected/admin/dashboard", "host": ["{{baseUrl}}"], "path": ["api", "v1", "protected", "admin", "dashboard"]}}, "response": []}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8082", "type": "string"}, {"key": "authToken", "value": "", "type": "string", "description": "Token JWT obtido após o login"}, {"key": "refreshToken", "value": "", "type": "string", "description": "Refresh Token JWT obtido após o login"}]}