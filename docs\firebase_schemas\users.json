{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Users Collection Schema", "description": "Schema para a collection 'users' do Firebase", "type": "object", "properties": {"userMID": {"type": "string", "description": "ID do MongoDB (ObjectID em formato hexadecimal) que identifica o motorista no banco de dados principal"}, "userId": {"type": "string", "description": "ID do próprio documento no Firebase, usado para auto-referência"}, "cpf": {"type": "string", "description": "CPF do motorista, usado como identificador único"}, "fullName": {"type": "string", "description": "Nome completo do motorista"}, "email": {"type": "string", "format": "email", "description": "Email do motorista"}, "phone": {"type": "string", "description": "Número de telefone do motorista"}, "status": {"type": "string", "description": "Status do motorista no aplicativo", "enum": ["active", "inactive", "create_password", "pending"]}, "typeUser": {"type": "string", "description": "Tipo de usuário", "enum": ["driver", "admin", "company"]}, "companyId": {"type": "string", "description": "ID da empresa à qual o motorista está vinculado (ObjectID em formato hexadecimal)"}, "active": {"type": "boolean", "description": "Indica se o motorista está ativo"}, "createdAt": {"type": "string", "format": "date-time", "description": "Data e hora de criação do registro"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Data e hora da última atualização do registro"}, "avatarURL": {"type": "string", "format": "uri", "description": "URL da foto do motorista"}, "verificationCode": {"type": "string", "description": "Código de verificação para ativação da conta"}, "firebaseEmail": {"type": "string", "format": "email", "description": "Email usado para autenticação no Firebase (geralmente <EMAIL>)"}, "uid": {"type": "string", "description": "UID do usuário no Firebase Authentication"}, "hasPassword": {"type": "boolean", "description": "Indica se o usuário já definiu uma senha"}, "activatedAt": {"type": "string", "format": "date-time", "description": "Data e hora de ativação da conta"}, "cnhCategory": {"type": "string", "description": "Categoria da CNH do motorista"}, "cnhExpiration": {"type": "string", "description": "Data de validade da CNH no formato DD/MM/YYYY"}, "modules": {"type": "object", "description": "Módulos do sistema aos quais o motorista tem acesso", "additionalProperties": {"type": "boolean"}}, "config": {"type": "object", "description": "Configurações específicas do motorista", "additionalProperties": {"type": "boolean"}}, "permissions": {"type": "array", "description": "Lista de permissões do motorista", "items": {"type": "string"}}, "timezone": {"type": "string", "description": "<PERSON>so hor<PERSON>rio do motorista"}, "timezoneOffset": {"type": "number", "description": "Diferença em minutos entre o fuso horário do motorista e UTC"}}, "required": ["userMID", "userId", "cpf", "fullName", "email", "typeUser", "companyId", "active", "createdAt", "updatedAt"]}