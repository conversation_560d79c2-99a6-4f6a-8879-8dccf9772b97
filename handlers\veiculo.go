package handlers

import (
	"context"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"time"
	"wedriverapigo/models"
	"wedriverapigo/utils"

	"github.com/mitchellh/mapstructure"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// VeiculoHandler gerencia as operações relacionadas a veículos
type VeiculoHandler struct {
	MongoClient *mongo.Client
	DBName      string
}

// NewVeiculoHandler cria um novo handler de veículos
func NewVeiculoHandler(client *mongo.Client, dbName string) *VeiculoHandler {
	return &VeiculoHandler{
		MongoClient: client,
		DBName:      dbName,
	}
}

// CreateVeiculo cria um novo veículo
// @Summary Criar Novo Veículo
// @Description Cria um novo registro de veículo associado à empresa do usuário autenticado.
// @Tags Veículos
// @Accept  json
// @Produce  json
// @Param   veiculo body models.Veiculo true "Dados do Veículo para criação"
// @Success 201 {object} models.Veiculo "Veículo criado com sucesso"
// @Failure 400 {object} map[string]string "error": "ID da empresa inválido" or "error": "Dados inválidos: {detalhe_do_erro}"
// @Failure 401 {object} map[string]string "error": "ID da empresa não encontrado no token"
// @Failure 500 {object} map[string]string "error": "Erro ao criar veículo"
// @Security BearerAuth
// @Router /veiculo [post]
func (h *VeiculoHandler) CreateVeiculo(c *gin.Context) {
	empresaIDStr, exists := c.Get("empresa_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "ID da empresa não encontrado no token"})
		return
	}

	empresaID, err := primitive.ObjectIDFromHex(empresaIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da empresa inválido"})
		return
	}

	// 1. Ler o payload JSON bruto para um mapa
	var payloadMap map[string]interface{}
	if err := c.ShouldBindJSON(&payloadMap); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Payload inválido: " + err.Error()})
		return
	}

	// 2. Extrair 'photo' (Base64 string) do mapa e removê-la
	var photoBase64StrFromPayload string
	if photoPayload, ok := payloadMap["photo"]; ok { // Espera "photo" no JSON
		if pStr, isString := photoPayload.(string); isString {
			photoBase64StrFromPayload = pStr
		}
		delete(payloadMap, "photo") // Remover antes do mapstructure
	}

	var veiculo models.Veiculo // Struct que será preenchida

	// 3. Usar mapstructure para decodificar o payloadMap (sem 'photo') em 'veiculo'
	decoderConfig := &mapstructure.DecoderConfig{
		Result:  &veiculo,
		TagName: "json",
		DecodeHook: mapstructure.ComposeDecodeHookFunc(
			utils.StringToTimeHookFuncRFC3339(),  // Hook para datas RFC3339
			utils.StringToTimeHookFuncDateOnly(), // Hook para datas YYYY-MM-DD
			utils.TimeToPointerTimeHookFunc(),    // Converte time.Time para *time.Time, tratando datas zero como nil
			// Adicione outros hooks se necessário (ex: StringToObjectIDHookFunc, etc.)
		),
	}
	decoder, err := mapstructure.NewDecoder(decoderConfig)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao inicializar decodificador: " + err.Error()})
		return
	}

	if err := decoder.Decode(payloadMap); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Erro ao decodificar dados do veículo: " + err.Error()})
		return
	}

	// 4. Configurar campos padrão e IDs
	now := time.Now().UTC()
	veiculo.ID = primitive.NewObjectID() // Gerar ID antes para usar no nome/caminho do arquivo
	veiculo.Empresa = empresaID
	veiculo.CreatedAt = now
	veiculo.UpdatedAt = now
	// Defina Ativo com base no payload se ele estiver presente, caso contrário, o padrão é true para novos.
	if activePayload, ok := payloadMap["active"]; ok {
		if activeBool, isBool := activePayload.(bool); isBool {
			veiculo.Ativo = activeBool
		} else {
			// Lidar com tipo inesperado para 'active' se necessário, ou ignorar e manter o padrão.
			veiculo.Ativo = true // Padrão se 'active' não for booleano
		}
	} else {
		veiculo.Ativo = true // Padrão se 'active' não estiver no payload
	}

	if veiculo.Documentos == nil {
		veiculo.Documentos = []models.Anexo{}
	}

	// 5. Processar a foto (Base64 string do payload original)
	if photoBase64StrFromPayload != "" {
		uploadsDir := os.Getenv("UPLOADS_DIR")
		if uploadsDir == "" {
			uploadsDir = "./uploads_data" // fallback
			log.Println("UPLOADS_DIR não definido, usando ./uploads_data")
		}
		// Usar veiculo.ID.Hex() como base do nome do arquivo para garantir unicidade
		subPath := filepath.Join("empresas", empresaID.Hex(), "veiculos", "fotos")
		generatedFileName, contentType, extension, errSf := utils.SaveBase64ToFile(photoBase64StrFromPayload, uploadsDir, subPath, veiculo.ID.Hex())
		if errSf != nil {
			log.Printf("Erro ao salvar foto do veículo %s para empresa %s: %v", veiculo.ID.Hex(), empresaID.Hex(), errSf)
			// Considerar se deve retornar erro ao cliente ou apenas logar
		} else {
			veiculo.Foto.FileID = veiculo.ID.Hex() // Ou algum outro identificador único se preferir
			veiculo.Foto.ContentType = contentType
			veiculo.Foto.Extension = extension
			// Armazenar o caminho relativo ao UPLOADS_DIR
			veiculo.Foto.RelativePath = filepath.ToSlash(filepath.Join(subPath, generatedFileName))
			// Opcional: veiculo.Foto.Url = construir a URL completa se aplicável
		}
	}
	// Não há mais veiculo.FotoBase64 para limpar

	// 6. Inserir no banco de dados
	veiculosCollection := h.MongoClient.Database(h.DBName).Collection("veiculos")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	_, err = veiculosCollection.InsertOne(ctx, veiculo)
	if err != nil {
		log.Printf("Erro ao inserir veículo: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao criar veículo"})
		return
	}

	c.JSON(http.StatusCreated, veiculo)
}

// GetVeiculos retorna todos os veículos da empresa com paginação
// @Summary Listar Veículos
// @Description Retorna uma lista paginada de veículos associados à empresa do usuário autenticado.
// @Tags Veículos
// @Produce  json
// @Param   page query int false "Número da página" default(1)
// @Param   limit query int false "Número de itens por página" default(10) maximum(100)
// @Success 200 {object} map[string]interface{} "data": []models.Veiculo, "total": int64, "page": int, "limit": int
// @Failure 400 {object} map[string]string "error": "ID da empresa inválido"
// @Failure 401 {object} map[string]string "error": "ID da empresa não encontrado no token"
// @Failure 500 {object} map[string]string "error": "Erro ao buscar veículos"
// @Security BearerAuth
// @Router /veiculo [get]
func (h *VeiculoHandler) GetVeiculos(c *gin.Context) {
	empresaIDStr, exists := c.Get("empresa_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "ID da empresa não encontrado no token"})
		return
	}
	empresaID, err := primitive.ObjectIDFromHex(empresaIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da empresa inválido"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if page < 1 {
		page = 1
	}
	if limit < 1 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}
	skip := (page - 1) * limit

	veiculosCollection := h.MongoClient.Database(h.DBName).Collection("veiculos")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	filter := bson.M{"empresa": empresaID}
	findOptions := options.Find()
	findOptions.SetLimit(int64(limit))
	findOptions.SetSkip(int64(skip))
	findOptions.SetSort(bson.M{"created_at": -1})

	cursor, err := veiculosCollection.Find(ctx, filter, findOptions)
	if err != nil {
		log.Printf("Erro ao buscar veículos: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar veículos"})
		return
	}
	defer cursor.Close(ctx)

	var veiculos []models.Veiculo
	if err = cursor.All(ctx, &veiculos); err != nil {
		log.Printf("Erro ao decodificar veículos: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao processar dados dos veículos"})
		return
	}

	total, err := veiculosCollection.CountDocuments(ctx, filter)
	if err != nil {
		log.Printf("Erro ao contar veículos: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao obter total de veículos"})
		return
	}

	apiBaseURL := os.Getenv("API_BASE_URL")
	staticRoutePrefix := os.Getenv("STATIC_ROUTE_PREFIX")
	// Ajustar Foto.URL para ser completa
	for i := range veiculos {
		if veiculos[i].Foto.RelativePath != "" {
			veiculos[i].Foto.URL = apiBaseURL + staticRoutePrefix + "/" + veiculos[i].Foto.RelativePath
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  veiculos,
		"total": total,
		"page":  page,
		"limit": limit,
	})
}

// GetVeiculoByID retorna um veículo específico pelo ID
// @Summary Obter Veículo por ID
// @Description Retorna os detalhes de um veículo específico, dado seu ID, se pertencer à empresa do usuário autenticado.
// @Tags Veículos
// @Produce  json
// @Param   id path string true "ID do Veículo"
// @Success 200 {object} models.Veiculo "Detalhes do Veículo"
// @Failure 400 {object} map[string]string "error": "ID da empresa inválido" or "error": "ID do veículo inválido"
// @Failure 401 {object} map[string]string "error": "ID da empresa não encontrado no token"
// @Failure 404 {object} map[string]string "error": "Veículo não encontrado ou não pertence à empresa"
// @Failure 500 {object} map[string]string "error": "Erro ao buscar veículo"
// @Security BearerAuth
// @Router /veiculo/{id} [get]
func (h *VeiculoHandler) GetVeiculoByID(c *gin.Context) {
	empresaIDStr, exists := c.Get("empresa_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "ID da empresa não encontrado no token"})
		return
	}
	empresaID, err := primitive.ObjectIDFromHex(empresaIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da empresa inválido"})
		return
	}

	veiculoIDStr := c.Param("id")
	veiculoID, err := primitive.ObjectIDFromHex(veiculoIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do veículo inválido"})
		return
	}

	veiculosCollection := h.MongoClient.Database(h.DBName).Collection("veiculos")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var veiculo models.Veiculo
	filter := bson.M{"_id": veiculoID, "empresa": empresaID}
	err = veiculosCollection.FindOne(ctx, filter).Decode(&veiculo)
	if err == mongo.ErrNoDocuments {
		c.JSON(http.StatusNotFound, gin.H{"error": "Veículo não encontrado ou não pertence à empresa"})
		return
	}
	if err != nil {
		log.Printf("Erro ao buscar veículo por ID: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar veículo"})
		return
	}

	if veiculo.Foto.RelativePath != "" {
		apiBaseURL := os.Getenv("API_BASE_URL")
		staticRoutePrefix := os.Getenv("STATIC_ROUTE_PREFIX")
		veiculo.Foto.URL = apiBaseURL + staticRoutePrefix + "/" + veiculo.Foto.RelativePath
	}

	c.JSON(http.StatusOK, veiculo)
}

// UpdateVeiculo atualiza um veículo existente
// @Summary Atualizar Veículo (PUT)
// @Description Atualiza completamente os dados de um veículo existente, dado seu ID. Requer que todos os campos do veículo sejam enviados.
// @Tags Veículos
// @Accept  json
// @Produce  json
// @Param   id path string true "ID do Veículo"
// @Param   veiculo body models.Veiculo true "Dados completos do Veículo para atualização"
// @Success 200 {object} models.Veiculo "Veículo atualizado com sucesso"
// @Failure 400 {object} map[string]string "error": "ID da empresa inválido" or "error": "ID do veículo inválido" or "error": "Dados inválidos: {detalhe_do_erro}"
// @Failure 401 {object} map[string]string "error": "ID da empresa não encontrado no token"
// @Failure 404 {object} map[string]string "error": "Veículo não encontrado ou não pertence à empresa"
// @Failure 500 {object} map[string]string "error": "Erro ao atualizar veículo"
// @Security BearerAuth
// @Router /veiculo/{id} [put]
func (h *VeiculoHandler) UpdateVeiculo(c *gin.Context) {
	empresaIDStr, exists := c.Get("empresa_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "ID da empresa não encontrado no token"})
		return
	}
	empresaID, err := primitive.ObjectIDFromHex(empresaIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da empresa inválido"})
		return
	}

	veiculoIDStr := c.Param("id")
	veiculoID, err := primitive.ObjectIDFromHex(veiculoIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do veículo inválido"})
		return
	}

	// Coleção de veículos
	veiculosCollection := h.MongoClient.Database(h.DBName).Collection("veiculos")
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second) // Aumentado timeout para operações de arquivo
	defer cancel()

	// 1. Buscar o veículo existente para preservar campos como CreatedAt e Foto (se não for atualizada)
	var existingVeiculo models.Veiculo
	filter := bson.M{"_id": veiculoID, "empresa": empresaID}
	if err := veiculosCollection.FindOne(ctx, filter).Decode(&existingVeiculo); err != nil {
		if err == mongo.ErrNoDocuments {
			c.JSON(http.StatusNotFound, gin.H{"error": "Veículo não encontrado ou não pertence à empresa"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar veículo existente: " + err.Error()})
		return
	}

	// 2. Ler o payload JSON bruto para um mapa
	var payloadMap map[string]interface{}
	if err := c.ShouldBindJSON(&payloadMap); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Payload inválido para PUT: " + err.Error()})
		return
	}

	// 3. Extrair 'photo' (Base64 string) do mapa e removê-la
	var photoBase64StrFromPayload string
	photoInPayload := false
	if photoPayload, ok := payloadMap["photo"]; ok {
		photoInPayload = true
		if pStr, isString := photoPayload.(string); isString {
			photoBase64StrFromPayload = pStr
		} else if photoPayload == nil { // Explicitamente null
			photoBase64StrFromPayload = ""
		}
		delete(payloadMap, "photo") // Remover antes do mapstructure
	}

	// 4. Inicializar updatedVeiculoData com valores que não devem ser zerados ou que vêm do existente
	updatedVeiculoData := models.Veiculo{
		ID:        veiculoID,                 // Da URL
		Empresa:   empresaID,                 // Do token
		CreatedAt: existingVeiculo.CreatedAt, // Preservar CreatedAt
		// Foto será tratada abaixo
	}

	// 5. Usar mapstructure para decodificar o payloadMap em updatedVeiculoData
	decoderConfig := &mapstructure.DecoderConfig{
		Result:  &updatedVeiculoData,
		TagName: "json",
		DecodeHook: mapstructure.ComposeDecodeHookFunc(
			utils.StringToTimeHookFuncRFC3339(),
			utils.StringToTimeHookFuncDateOnly(),
			utils.TimeToPointerTimeHookFunc(), // Converte time.Time para *time.Time, tratando datas zero como nil
		),
	}
	decoder, err := mapstructure.NewDecoder(decoderConfig)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao inicializar decodificador de atualização: " + err.Error()})
		return
	}
	if err := decoder.Decode(payloadMap); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Erro ao decodificar dados de atualização: " + err.Error()})
		return
	}

	// 6. Definir campos gerenciados pelo sistema
	updatedVeiculoData.UpdatedAt = time.Now().UTC()

	// Para PUT, se 'active' não estava no payload, o valor padrão (false para bool) de mapstructure será usado.
	// Se a intenção é manter existingVeiculo.Ativo se 'active' não vier, isso deve ser explícito:
	if _, activePresentInPayload := payloadMap["active"]; !activePresentInPayload {
		updatedVeiculoData.Ativo = existingVeiculo.Ativo
	}

	// 7. Processar a foto
	uploadsDir := os.Getenv("UPLOADS_DIR")
	if uploadsDir == "" {
		uploadsDir = "./uploads_data"
		log.Println("UPLOADS_DIR não definido, usando ./uploads_data")
	}
	subPath := filepath.Join("empresas", empresaID.Hex(), "veiculos", "fotos")

	if photoInPayload {
		if photoBase64StrFromPayload != "" { // Nova foto fornecida
			// Remover foto antiga, se existir
			if existingVeiculo.Foto.RelativePath != "" {
				oldPhotoPath := filepath.Join(uploadsDir, existingVeiculo.Foto.RelativePath)
				if err := os.Remove(oldPhotoPath); err != nil {
					log.Printf("Erro ao remover foto antiga %s do veículo %s: %v", oldPhotoPath, veiculoID.Hex(), err)
				}
			}

			generatedFileName, contentType, extension, errSf := utils.SaveBase64ToFile(photoBase64StrFromPayload, uploadsDir, subPath, veiculoID.Hex())
			if errSf != nil {
				log.Printf("Erro ao salvar nova foto para veículo %s (PUT): %v", veiculoID.Hex(), errSf)
				// Não retornar erro ao cliente, mas manter a foto antiga ou nenhuma foto
				updatedVeiculoData.Foto = models.FotoMetadata{} // Ou manter existingVeiculo.Foto se a nova falhar
			} else {
				updatedVeiculoData.Foto.FileID = veiculoID.Hex()
				updatedVeiculoData.Foto.ContentType = contentType
				updatedVeiculoData.Foto.Extension = extension
				updatedVeiculoData.Foto.RelativePath = filepath.ToSlash(filepath.Join(subPath, generatedFileName))
			}
		} else { // Foto explicitamente definida como vazia/null no payload
			if existingVeiculo.Foto.RelativePath != "" {
				oldPhotoPath := filepath.Join(uploadsDir, existingVeiculo.Foto.RelativePath)
				if err := os.Remove(oldPhotoPath); err != nil {
					log.Printf("Erro ao remover foto existente %s do veículo %s (PUT): %v", oldPhotoPath, veiculoID.Hex(), err)
				}
			}
			updatedVeiculoData.Foto = models.FotoMetadata{}
		}
	} else { // Campo 'photo' não estava no payload
		// Para PUT, a omissão de um campo geralmente significa que ele deve ser removido ou zerado.
		// Portanto, removemos a foto existente.
		if existingVeiculo.Foto.RelativePath != "" {
			oldPhotoPath := filepath.Join(uploadsDir, existingVeiculo.Foto.RelativePath)
			if err := os.Remove(oldPhotoPath); err != nil {
				log.Printf("Erro ao remover foto (omitida no PUT) %s do veículo %s: %v", oldPhotoPath, veiculoID.Hex(), err)
			}
		}
		updatedVeiculoData.Foto = models.FotoMetadata{}
	}

	// 8. Usar ReplaceOne para substituir o documento inteiro
	// O filtro já foi definido acima para buscar existingVeiculo
	result, err := veiculosCollection.ReplaceOne(ctx, filter, updatedVeiculoData)
	if err != nil {
		log.Printf("Erro ao atualizar veículo (PUT ReplaceOne): %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar veículo"})
		return
	}

	if result.MatchedCount == 0 {
		// Isso não deveria acontecer se o FindOne acima teve sucesso, mas é uma verificação de segurança
		c.JSON(http.StatusNotFound, gin.H{"error": "Veículo não encontrado para atualização (PUT)"})
		return
	}

	// Construir URL da foto para a resposta
	if updatedVeiculoData.Foto.RelativePath != "" {
		apiBaseURL := os.Getenv("API_BASE_URL")
		staticRoutePrefix := os.Getenv("STATIC_ROUTE_PREFIX")
		if apiBaseURL != "" && staticRoutePrefix != "" {
			updatedVeiculoData.Foto.URL = apiBaseURL + staticRoutePrefix + updatedVeiculoData.Foto.RelativePath
		} else {
			log.Printf("API_BASE_URL ou STATIC_ROUTE_PREFIX não configurados para UpdateVeiculo, Foto.URL de %s não será completa", updatedVeiculoData.ID.Hex())
		}
	}

	c.JSON(http.StatusOK, updatedVeiculoData)
}

// PatchVeiculo atualiza parcialmente um veículo existente
// @Summary Atualizar Parcialmente Veículo (PATCH)
// @Description Atualiza parcialmente os dados de um veículo existente, dado seu ID. Apenas os campos fornecidos na requisição serão alterados.
// @Tags Veículos
// @Accept  json
// @Produce  json
// @Param   id path string true "ID do Veículo"
// @Param   veiculo_update body map[string]interface{} true "Campos do Veículo para atualização parcial. Datas devem ser strings no formato RFC3339 (YYYY-MM-DDTHH:MM:SSZ) ou YYYY-MM-DD."
// @Success 200 {object} models.Veiculo "Veículo atualizado com sucesso"
// @Failure 400 {object} map[string]string "error": "ID da empresa inválido" or "error": "ID do veículo inválido" or "error": "Dados inválidos" or "error": "Nenhum campo válido para atualização fornecido..." or "error": "Formato de data inválido..."
// @Failure 401 {object} map[string]string "error": "ID da empresa não encontrado no token"
// @Failure 404 {object} map[string]string "error": "Veículo não encontrado ou não pertence à empresa"
// @Failure 500 {object} map[string]string "error": "Erro ao atualizar veículo"
// @Security BearerAuth
// @Router /veiculo/{id} [patch]
func (h *VeiculoHandler) PatchVeiculo(c *gin.Context) {
	empresaIDStr, exists := c.Get("empresa_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "ID da empresa não encontrado no token"})
		return
	}
	empresaID, err := primitive.ObjectIDFromHex(empresaIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da empresa inválido"})
		return
	}

	veiculoIDStr := c.Param("id")
	veiculoID, err := primitive.ObjectIDFromHex(veiculoIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do veículo inválido"})
		return
	}

	veiculosCollection := h.MongoClient.Database(h.DBName).Collection("veiculos")
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second) // Timeout aumentado
	defer cancel()

	// 1. Buscar o veículo existente para verificar se ele existe e para obter informações da foto antiga
	var existingVeiculo models.Veiculo
	filter := bson.M{"_id": veiculoID, "empresa": empresaID}
	if err := veiculosCollection.FindOne(ctx, filter).Decode(&existingVeiculo); err != nil {
		if err == mongo.ErrNoDocuments {
			c.JSON(http.StatusNotFound, gin.H{"error": "Veículo não encontrado ou não pertence à empresa"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar veículo para PATCH: " + err.Error()})
		return
	}

	var payload map[string]interface{}
	if err := c.ShouldBindJSON(&payload); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos para PATCH: " + err.Error()})
		return
	}

	updateFields := bson.M{}
	uploadsDir := os.Getenv("UPLOADS_DIR")
	if uploadsDir == "" {
		uploadsDir = "./uploads_data"
		log.Println("UPLOADS_DIR não definido, usando ./uploads_data para PATCH")
	}
	// SubPath consistente para fotos de veículos desta empresa
	empresaVeiculosFotosSubPath := filepath.Join("empresas", empresaID.Hex(), "veiculos", "fotos")

	// 2. Processar 'photo' (Base64 string) primeiro, se existir no payload
	photoKeyExistsInPayload := false
	var photoBase64StrFromPayload string

	if photoPayloadValue, ok := payload["photo"]; ok {
		photoKeyExistsInPayload = true
		if pStr, isString := photoPayloadValue.(string); isString {
			photoBase64StrFromPayload = pStr
		} else if photoPayloadValue == nil { // Tratar 'photo: null' como intenção de remover
			photoBase64StrFromPayload = ""
		}
		delete(payload, "photo") // Remover 'photo' do payload para não ser processado pelo loop genérico abaixo
	}

	if photoKeyExistsInPayload {
		// Remover foto antiga do sistema de arquivos, se ela existir
		if existingVeiculo.Foto.RelativePath != "" {
			oldPhotoFileSystemPath := filepath.Join(uploadsDir, existingVeiculo.Foto.RelativePath)
			if err := os.Remove(oldPhotoFileSystemPath); err != nil {
				log.Printf("Erro ao remover foto antiga %s do veículo %s durante PATCH: %v", oldPhotoFileSystemPath, veiculoID.Hex(), err)
				// Não bloquear a operação por isso, mas logar. A nova foto (ou a limpeza dos metadados) prosseguirá.
			}
		}

		if photoBase64StrFromPayload != "" { // Nova foto fornecida ou foto existente para ser reprocessada (embora o último seja incomum para PATCH)
			generatedFileName, contentType, extension, errSf := utils.SaveBase64ToFile(photoBase64StrFromPayload, uploadsDir, empresaVeiculosFotosSubPath, veiculoID.Hex())
			if errSf != nil {
				log.Printf("Erro ao salvar nova foto durante PATCH para veículo %s: %v", veiculoID.Hex(), errSf)
				// Se o salvamento da nova foto falhar, limpar os metadados da foto para evitar inconsistência.
				updateFields["foto"] = models.FotoMetadata{}
			} else {
				newFotoMetadata := models.FotoMetadata{
					FileID:       veiculoID.Hex(), // Usar ID do veículo como parte da identificação da foto
					ContentType:  contentType,
					Extension:    extension,
					RelativePath: filepath.ToSlash(filepath.Join(empresaVeiculosFotosSubPath, generatedFileName)),
				}
				updateFields["foto"] = newFotoMetadata // Definir para atualizar o subdocumento 'foto'
			}
		} else { // photoBase64StrFromPayload é "" (string vazia ou era 'photo: null'), indicando remoção explícita da foto
			updateFields["foto"] = models.FotoMetadata{} // Limpar metadados da foto no banco de dados
		}
	}

	// 3. Construir mapa de campos permitidos (allowedFields) e campos de data (dateFields) usando reflexão
	veiculoModelType := reflect.TypeOf(models.Veiculo{})
	allowedFields := map[string]bool{
		"placa":          true,
		"marca":          true,
		"modelo":         true,
		"cor":            true,
		"anoFabricacao":  true,
		"anoModelo":      true,
		"chassi":         true,
		"renavam":        true,
		"combustivel":    true,
		"capacidade":     true,
		"proprietario":   true,
		"uf":             true,
		"cidade":         true,
		"ativo":          true,
		"dataRegistro":   true,
		"dataVencimento": true,
		"modelYear":      true, // Adicionado para permitir atualização
		"notes":          true, // Adicionado para permitir atualização
		"nickname":       true, // Adicionado para permitir atualização
	}
	dateFields := make(map[string]bool) // Indica se jsonKey é um campo de data

	for i := 0; i < veiculoModelType.NumField(); i++ {
		field := veiculoModelType.Field(i)
		jsonTagValue := field.Tag.Get("json")
		bsonTagValue := field.Tag.Get("bson")

		if jsonTagValue == "" || jsonTagValue == "-" { // Ignorar campos não expostos via JSON
			continue
		}
		jsonKey := strings.Split(jsonTagValue, ",")[0]

		if bsonTagValue == "" || bsonTagValue == "-" { // Ignorar campos não persistidos no BSON ou com tag explícita "-"
			continue
		}
		bsonKey := strings.Split(bsonTagValue, ",")[0]

		// Definir quais campos são atualizáveis. Excluir campos gerenciados pelo sistema ou imutáveis.
		// 'foto' (o objeto FotoMetadata) é tratado pela lógica de 'photo' (string base64) acima.
		// 'ativo' É um campo atualizável.
		if bsonKey != "_id" && bsonKey != "empresa" && bsonKey != "created_at" && bsonKey != "updated_at" && bsonKey != "foto" {
			allowedFields[jsonKey] = true
			if field.Type == reflect.TypeOf(time.Time{}) || field.Type == reflect.TypeOf((*time.Time)(nil)) {
				dateFields[jsonKey] = true
			}
		}
	}

	parsedTimeSuccessfully := true
	for fieldName, value := range payload {
		if !allowedFields[fieldName] {
			log.Printf("Campo não permitido ou desconhecido para PATCH: %s", fieldName)
			continue // Ignora campos não permitidos
		}

		if dateFields[fieldName] {
			if value == nil {
				updateFields[fieldName] = nil
				continue
			}
			dateStr, ok := value.(string)
			if !ok {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Formato de data inválido para o campo " + fieldName + ". Esperado string ou nulo."})
				parsedTimeSuccessfully = false
				break
			}
			parsedTime, err := parseDate(dateStr)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Formato de data inválido para o campo " + fieldName + ". Use RFC3339 (ex: YYYY-MM-DDTHH:MM:SSZ) ou YYYY-MM-DD."})
				parsedTimeSuccessfully = false
				break
			}
			updateFields[fieldName] = parsedTime
		} else {
			updateFields[fieldName] = value
		}
	}

	if !parsedTimeSuccessfully {
		return
	}

	if len(updateFields) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Nenhum campo válido para atualização fornecido ou campos não permitidos."})
		return
	}

	updateFields["updated_at"] = time.Now().UTC()

	// veiculosCollection, ctx, cancel, and filter are already defined at the beginning of the function.
	// Using the existing variables.
	update := bson.M{"$set": updateFields}

	result, err := veiculosCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		log.Printf("Erro ao aplicar PATCH no veículo: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar veículo"})
		return
	}

	if result.MatchedCount == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Veículo não encontrado ou não pertence à empresa"})
		return
	}

	var veiculoAtualizado models.Veiculo
	err = veiculosCollection.FindOne(ctx, filter).Decode(&veiculoAtualizado)
	if err != nil {
		log.Printf("Erro ao buscar veículo após PATCH: %v", err)
		c.JSON(http.StatusOK, gin.H{"message": "Veículo atualizado com sucesso, mas erro ao retornar os dados completos."})
		return
	}

	// Construir URL da foto para a resposta
	if veiculoAtualizado.Foto.RelativePath != "" {
		apiBaseURL := os.Getenv("API_BASE_URL")
		staticRoutePrefix := os.Getenv("STATIC_ROUTE_PREFIX")
		if apiBaseURL != "" && staticRoutePrefix != "" {
			veiculoAtualizado.Foto.URL = apiBaseURL + staticRoutePrefix + veiculoAtualizado.Foto.RelativePath
		} else {
			log.Printf("API_BASE_URL ou STATIC_ROUTE_PREFIX não configurados para PatchVeiculo, Foto.URL de %s não será completa", veiculoAtualizado.ID.Hex())
		}
	}

	c.JSON(http.StatusOK, veiculoAtualizado)
}

// DeleteVeiculo realiza a exclusão lógica de um veículo
// @Summary Excluir Veículo (Lógico)
// @Description Realiza a exclusão lógica de um veículo (define o campo 'ativo' como false), dado seu ID.
// @Tags Veículos
// @Produce  json
// @Param   id path string true "ID do Veículo"
// @Success 200 {object} map[string]string "message": "Veículo excluído com sucesso"
// @Failure 400 {object} map[string]string "error": "ID da empresa inválido" or "error": "ID do veículo inválido"
// @Failure 401 {object} map[string]string "error": "ID da empresa não encontrado no token"
// @Failure 404 {object} map[string]string "error": "Veículo não encontrado ou não pertence à empresa"
// @Failure 500 {object} map[string]string "error": "Erro ao excluir veículo"
// @Security BearerAuth
// @Router /veiculo/{id} [delete]
func (h *VeiculoHandler) DeleteVeiculo(c *gin.Context) {
	empresaIDStr, exists := c.Get("empresa_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "ID da empresa não encontrado no token"})
		return
	}
	empresaID, err := primitive.ObjectIDFromHex(empresaIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da empresa inválido"})
		return
	}

	veiculoIDStr := c.Param("id")
	veiculoID, err := primitive.ObjectIDFromHex(veiculoIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do veículo inválido"})
		return
	}

	veiculosCollection := h.MongoClient.Database(h.DBName).Collection("veiculos")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	filter := bson.M{"_id": veiculoID, "empresa": empresaID}
	update := bson.M{
		"$set": bson.M{
			"ativo":      false,
			"updated_at": time.Now().UTC(),
		},
	}

	result, err := veiculosCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		log.Printf("Erro ao excluir veículo: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao excluir veículo"})
		return
	}

	if result.MatchedCount == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Veículo não encontrado ou não pertence à empresa"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Veículo excluído com sucesso"})
}

// parseDate tenta analisar uma string de data nos formatos RFC3339 ou YYYY-MM-DD
func parseDate(dateStr string) (time.Time, error) {
	parsedTime, err := time.Parse(time.RFC3339, dateStr)
	if err == nil {
		return parsedTime, nil
	}
	parsedTime, err = time.Parse("2006-01-02", dateStr) // Formato YYYY-MM-DD
	return parsedTime, err
}
