basePath: /api/v1
definitions:
  handlers.CompletarCadastroRequest:
    properties:
      email_key:
        type: string
      name:
        type: string
      password:
        description: Exige senha com no mínimo 6 caracteres
        minLength: 6
        type: string
    required:
    - email_key
    - name
    - password
    type: object
  handlers.CompletarResetSenhaRequest:
    properties:
      new_password:
        description: Exige senha com no mínimo 6 caracteres
        minLength: 6
        type: string
      reset_key:
        type: string
    required:
    - new_password
    - reset_key
    type: object
  handlers.IniciarCadastroRequest:
    properties:
      email:
        type: string
    required:
    - email
    type: object
  handlers.IniciarResetSenhaRequest:
    properties:
      email:
        type: string
    required:
    - email
    type: object
  handlers.LoginRequest:
    properties:
      email:
        type: string
      password:
        type: string
    required:
    - email
    - password
    type: object
  handlers.RefreshTokenRequest:
    properties:
      refresh_token:
        type: string
    required:
    - refresh_token
    type: object
  handlers.ValidarCadastroRequest:
    properties:
      email_key:
        type: string
    required:
    - email_key
    type: object
  handlers.ValidarResetSenhaRequest:
    properties:
      reset_key:
        type: string
    required:
    - reset_key
    type: object
  handlers.VerifiedMotoristaData:
    properties:
      activated_at:
        description: Vem de activatedAt (Firestore)
        type: string
      active:
        description: Derivado de status == "active" (Firestore) -> models.Motorista.Ativo
        type: boolean
      app_status:
        description: Vem de status (Firestore) -> models.Motorista.AppStatus
        type: string
      cnh_category:
        description: models.Motorista.CNHCategoria
        type: string
      cnh_expiration:
        description: '"DD/MM/YYYY" do Firestore (cnhValidade)'
        type: string
      cpf:
        description: models.Motorista.CPF
        type: string
      email:
        description: models.Motorista.Email
        type: string
      firebase_auth_uid:
        description: Vem de uid (Auth UID do Firestore)
        type: string
      firebase_email:
        description: Campos do Firestore que podem ser úteis para referência ou para
          adicionar ao models.Motorista
        type: string
      firestore_created_at:
        description: Vem de createdAt (Firestore)
        type: string
      firestore_updated_at:
        description: Vem de updatedAt (Firestore)
        type: string
      firestore_user_id:
        description: Vem de userId (Firestore doc ID) -> models.Motorista.FirestoreUserID
        type: string
      modules:
        additionalProperties:
          type: boolean
        description: models.Motorista.Modulos
        type: object
      name:
        description: Campos principais para pré-preenchimento do formulário de criação
          do motorista
        type: string
      phone:
        description: models.Motorista.Telefone
        type: string
      photo_url:
        description: Vem de avatarURL (Firestore) -> models.Motorista.Foto.URL (ou
          campo similar)
        type: string
      type_user:
        description: Vem de typeUser (Firestore) -> models.Motorista.TipoUsuario
        type: string
      verification_code:
        description: models.Motorista.CodigoVerificacao
        type: string
    type: object
  models.Anexo:
    properties:
      data_upload:
        type: string
      description:
        type: string
      id:
        type: string
      name:
        type: string
      type:
        type: string
      url:
        type: string
    type: object
  models.FotoMetadata:
    properties:
      content_type:
        description: 'Ex: image/jpeg'
        type: string
      extension:
        description: 'Ex: .jpg, .png'
        type: string
      file_id:
        description: ID usado para nomear o arquivo base
        type: string
      url:
        description: URL completa, agora persistida
        type: string
    type: object
  models.Motorista:
    properties:
      activated_at:
        description: Novos campos do Firestore para sincronização
        type: string
      active:
        description: Mapeado de 'active' no JSON
        type: boolean
      appStatus:
        description: e.g., "pending"
        type: string
      attachments:
        items:
          $ref: '#/definitions/models.Anexo'
        type: array
      birthDate:
        description: '''birthDate'' no JSON'
        type: string
      bloodType:
        type: string
      cep:
        description: Campos de Endereco (achatados)
        type: string
      city:
        type: string
      cnh:
        description: Campos de CNH (achatados)
        type: string
      cnhCategory:
        type: string
      cnhExpiration:
        type: string
      cnhIssuanceDate:
        type: string
      cnhState:
        type: string
      complement:
        type: string
      config:
        additionalProperties:
          type: boolean
        description: Campos de configuração e permissões Firestore
        type: object
      cpf:
        type: string
      created_at:
        type: string
      email:
        type: string
      emergencyContact:
        type: string
      emergencyPhone:
        type: string
      empresa:
        description: Já existe, será preenchido pelo token
        type: string
      firebase_auth_uid:
        type: string
      firebase_email:
        type: string
      firestore_user_id:
        description: ID do documento do usuário no Firestore
        type: string
      gender:
        type: string
      id:
        type: string
      modules:
        additionalProperties:
          type: boolean
        type: object
      name:
        description: Campos específicos do motorista do JSON
        type: string
      neighborhood:
        type: string
      number:
        type: string
      observations:
        type: string
      permissions:
        items:
          type: string
        type: array
      phone:
        description: '''phone'' no JSON'
        type: string
      photo:
        $ref: '#/definitions/models.FotoMetadata'
      rg:
        type: string
      state:
        type: string
      street:
        type: string
      timezone:
        type: string
      timezoneOffset:
        type: integer
      typeUser:
        type: string
      updated_at:
        type: string
      verificationCode:
        type: string
    type: object
  models.Veiculo:
    properties:
      active:
        type: boolean
      brand:
        type: string
      chassisNumber:
        type: string
      color:
        type: string
      created_at:
        type: string
      documents:
        items:
          $ref: '#/definitions/models.Anexo'
        type: array
      empresa:
        description: Associado à empresa do token
        type: string
      fuelType:
        type: string
      id:
        type: string
      insuranceCompany:
        type: string
      insuranceExpirationDate:
        description: Usando ponteiro para permitir nulo
        type: string
      insurancePolicy:
        type: string
      mileage:
        type: integer
      model:
        type: string
      modelYear:
        type: string
      nickname:
        type: string
      notes:
        type: string
      observations:
        type: string
      photo:
        $ref: '#/definitions/models.FotoMetadata'
      plate:
        type: string
      qrCode:
        type: string
      renavam:
        type: string
      status:
        type: string
      tankCapacity:
        type: integer
      type:
        type: string
      updated_at:
        type: string
      year:
        type: string
    type: object
host: wedrivergoapi.srv.weso.com.br
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: API para gerenciamento e operações da Wedriver.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: WedriverApiGO API
  version: "1.0"
paths:
  /auth/login:
    post:
      consumes:
      - application/json
      description: Autentica um usuário com email e senha e retorna tokens JWT.
      parameters:
      - description: Credenciais de Login
        in: body
        name: login
        required: true
        schema:
          $ref: '#/definitions/handlers.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 'token": "jwt_token", "refresh_token": "jwt_refresh_token",
            "expires_at": **********, "user_id": "user_hex_id", "name": "User Name",
            "roles": ["role1", "role2"], "empresa_id": "empresa_hex_id'
          schema:
            additionalProperties: true
            type: object
        "400":
          description: 'error": "Dados inválidos: ...'
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: 'error": "Credenciais inválidas" or "error": "Usuário não está
            ativo'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao processar login" or "error": "Erro ao gerar
            token de autenticação'
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Login de Usuário
      tags:
      - Autenticação
  /auth/refresh:
    post:
      consumes:
      - application/json
      description: Gera um novo token de acesso JWT usando um refresh token válido.
      parameters:
      - description: Refresh Token
        in: body
        name: refresh
        required: true
        schema:
          $ref: '#/definitions/handlers.RefreshTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 'token": "new_jwt_token", "expires_at": **********'
          schema:
            additionalProperties: true
            type: object
        "400":
          description: 'error": "Dados inválidos: ...'
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: 'error": "Refresh token inválido ou expirado" or "error": "Usuário
            não está ativo'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao processar refresh token" or "error": "Erro
            ao gerar token de autenticação'
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Refresh de Token JWT
      tags:
      - Autenticação
  /cadastro/completar:
    post:
      consumes:
      - application/json
      description: Finaliza o processo de cadastro utilizando a chave de email validada,
        nome e senha. Cria a empresa associada e ativa o usuário.
      parameters:
      - description: Dados para completar o cadastro
        in: body
        name: completar_cadastro
        required: true
        schema:
          $ref: '#/definitions/handlers.CompletarCadastroRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 'message": "Cadastro concluído com sucesso!'
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: 'error": "Dados inválidos: {detalhe_do_erro}'
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: 'error": "Chave de ativação inválida.'
          schema:
            additionalProperties:
              type: string
            type: object
        "409":
          description: 'error": "Este cadastro já foi completado anteriormente.'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao validar chave de ativação." or "error": "Erro
            ao configurar a conta da empresa." or "error": "Erro ao processar informações
            de segurança." or "error": "Erro ao finalizar o cadastro.'
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Completar Cadastro de Usuário e Empresa
      tags:
      - Cadastro
  /cadastro/iniciar:
    post:
      consumes:
      - application/json
      description: Recebe um email, cria um pré-cadastro para o usuário, gera uma
        chave de confirmação e envia por email.
      parameters:
      - description: Email para iniciar o cadastro
        in: body
        name: cadastro
        required: true
        schema:
          $ref: '#/definitions/handlers.IniciarCadastroRequest'
      produces:
      - application/json
      responses:
        "201":
          description: 'message": "Processo de cadastro iniciado. Verifique seu email
            para o código de confirmação.'
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: 'error": "Dados inválidos: {detalhe_do_erro}'
          schema:
            additionalProperties:
              type: string
            type: object
        "409":
          description: 'error": "Este email já está cadastrado." or "error": "Cadastro
            pendente para este email...'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao iniciar processo de cadastro.'
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Iniciar Processo de Cadastro
      tags:
      - Cadastro
  /cadastro/validar:
    post:
      consumes:
      - application/json
      description: Verifica se uma chave de email (enviada anteriormente) é válida,
        não expirou e não foi utilizada.
      parameters:
      - description: Chave de email para validação
        in: body
        name: validacao
        required: true
        schema:
          $ref: '#/definitions/handlers.ValidarCadastroRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 'message": "Chave de email válida.", "email": "<EMAIL>'
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: 'error": "Dados inválidos: {detalhe_do_erro}'
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: 'error": "Chave de ativação inválida.'
          schema:
            additionalProperties:
              type: string
            type: object
        "409":
          description: 'error": "Este cadastro já foi completado.'
          schema:
            additionalProperties:
              type: string
            type: object
        "410":
          description: 'error": "Chave de ativação expirada.'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao validar chave de ativação.'
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Validar Chave de Email para Cadastro
      tags:
      - Cadastro
  /identificacao:
    get:
      consumes:
      - application/json
      description: Retorna uma lista de todas as identificações com opções de filtragem.
        Se nenhuma data for especificada, retorna apenas as identificações do dia
        atual.
      parameters:
      - description: Filtrar por ID do motorista (userMID)
        in: query
        name: motorista
        type: string
      - description: Filtrar por placa do veículo
        in: query
        name: placa
        type: string
      - description: Filtrar por tipo de identificação
        in: query
        name: tipo
        type: string
      - description: Filtrar por data de criação (início) no formato YYYY-MM-DD. Se
          não for fornecido, usa a data atual.
        in: query
        name: data_inicio
        type: string
      - description: Filtrar por data de criação (fim) no formato YYYY-MM-DD. Se não
          for fornecido, usa a data atual.
        in: query
        name: data_fim
        type: string
      - description: 'Se true, retorna todas as identificações sem filtro de data.
          Padrão: false'
        in: query
        name: todos
        type: boolean
      - description: 'Limite de resultados por página (padrão: 50)'
        in: query
        name: limit
        type: integer
      - description: Número de resultados para pular (para paginação)
        in: query
        name: skip
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Lista todas as identificações
      tags:
      - identificacoes
  /identificacao/{id}:
    get:
      consumes:
      - application/json
      description: Retorna os detalhes de uma identificação específica
      parameters:
      - description: ID da identificação
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Obtém uma identificação pelo ID
      tags:
      - identificacoes
  /identificacao/motorista/{motorista_id}:
    get:
      consumes:
      - application/json
      description: Retorna uma lista de identificações filtradas por ID do motorista
      parameters:
      - description: ID do motorista
        in: path
        name: motorista_id
        required: true
        type: string
      - description: 'Limite de resultados por página (padrão: 50)'
        in: query
        name: limit
        type: integer
      - description: Número de resultados para pular (para paginação)
        in: query
        name: skip
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Lista identificações por motorista
      tags:
      - identificacoes
  /identificacao/veiculo/{placa}:
    get:
      consumes:
      - application/json
      description: Retorna uma lista de identificações filtradas por placa do veículo
      parameters:
      - description: Placa do veículo
        in: path
        name: placa
        required: true
        type: string
      - description: 'Limite de resultados por página (padrão: 50)'
        in: query
        name: limit
        type: integer
      - description: Número de resultados para pular (para paginação)
        in: query
        name: skip
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Lista identificações por veículo
      tags:
      - identificacoes
  /motorista:
    get:
      description: Retorna uma lista paginada de motoristas associados à empresa do
        usuário autenticado.
      parameters:
      - default: 1
        description: Número da página
        in: query
        name: page
        type: integer
      - default: 10
        description: Número de itens por página
        in: query
        maximum: 100
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 'data": []models.Motorista, "total": int64, "page": int, "limit":
            int'
          schema:
            additionalProperties: true
            type: object
        "400":
          description: 'error": "ID da empresa inválido'
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: 'error": "ID da empresa não encontrado no token'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao buscar motoristas'
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Listar Motoristas
      tags:
      - Motoristas
    post:
      consumes:
      - application/json
      description: Cria um novo registro de motorista associado à empresa do usuário
        autenticado.
      parameters:
      - description: Dados do Motorista para criação
        in: body
        name: motorista
        required: true
        schema:
          $ref: '#/definitions/models.Motorista'
      produces:
      - application/json
      responses:
        "201":
          description: Motorista criado com sucesso
          schema:
            $ref: '#/definitions/models.Motorista'
        "400":
          description: 'error": "ID da empresa inválido" or "error": "Dados inválidos:
            {detalhe_do_erro}'
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: 'error": "ID da empresa não encontrado no token'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao criar motorista'
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Criar Novo Motorista
      tags:
      - Motoristas
  /motorista/{id}:
    get:
      description: Retorna os detalhes de um motorista específico, dado seu ID, se
        pertencer à empresa do usuário autenticado.
      parameters:
      - description: ID do Motorista
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Detalhes do Motorista
          schema:
            $ref: '#/definitions/models.Motorista'
        "400":
          description: 'error": "ID da empresa inválido" or "error": "ID do motorista
            inválido'
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: 'error": "ID da empresa não encontrado no token'
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: 'error": "Motorista não encontrado ou não pertence à empresa'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao buscar motorista'
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Obter Motorista por ID
      tags:
      - Motoristas
    patch:
      consumes:
      - application/json
      description: Atualiza parcialmente os dados de um motorista existente, dado
        seu ID. Apenas os campos fornecidos na requisição serão alterados.
      parameters:
      - description: ID do Motorista
        in: path
        name: id
        required: true
        type: string
      - description: Campos do Motorista para atualização parcial. Datas devem ser
          strings no formato RFC3339 (YYYY-MM-DDTHH:MM:SSZ) ou YYYY-MM-DD.
        in: body
        name: motorista_update
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Motorista atualizado com sucesso
          schema:
            $ref: '#/definitions/models.Motorista'
        "400":
          description: 'error": "ID da empresa inválido" or "error": "ID do motorista
            inválido" or "error": "Dados inválidos" or "error": "Nenhum campo válido
            para atualização fornecido..." or "error": "Formato de data inválido...'
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: 'error": "ID da empresa não encontrado no token'
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: 'error": "Motorista não encontrado ou não pertence à empresa'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao atualizar motorista'
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Atualizar Parcialmente Motorista (PATCH)
      tags:
      - Motoristas
    put:
      consumes:
      - application/json
      description: Atualiza completamente os dados de um motorista existente, dado
        seu ID. Requer que todos os campos do motorista sejam enviados.
      parameters:
      - description: ID do Motorista
        in: path
        name: id
        required: true
        type: string
      - description: Dados completos do Motorista para atualização
        in: body
        name: motorista
        required: true
        schema:
          $ref: '#/definitions/models.Motorista'
      produces:
      - application/json
      responses:
        "200":
          description: Motorista atualizado com sucesso
          schema:
            $ref: '#/definitions/models.Motorista'
        "400":
          description: 'error": "ID da empresa inválido" or "error": "ID do motorista
            inválido" or "error": "Dados inválidos: {detalhe_do_erro}'
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: 'error": "ID da empresa não encontrado no token'
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: 'error": "Motorista não encontrado ou não pertence à empresa'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao atualizar motorista'
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Atualizar Motorista (PUT)
      tags:
      - Motoristas
  /motorista/ativar/{id}:
    post:
      consumes:
      - application/json
      description: Busca um motorista no Firebase pelo ID, copia os dados para o MongoDB
        e atualiza o Firebase com o ID do MongoDB
      parameters:
      - description: ID do motorista no Firebase
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Motorista ativado com sucesso
          schema:
            $ref: '#/definitions/models.Motorista'
        "400":
          description: 'error": "ID não fornecido'
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: 'error": "Motorista não encontrado no Firebase'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro interno do servidor'
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Ativar motorista do Firebase
      tags:
      - Motoristas
  /motorista/verificar-codigo:
    get:
      description: Busca os dados de um pré-cadastro de motorista no Firestore usando
        um código de verificação.
      parameters:
      - description: Código de Verificação do Motorista
        in: query
        name: code
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Dados do motorista encontrados
          schema:
            $ref: '#/definitions/handlers.VerifiedMotoristaData'
        "400":
          description: 'error: \"Código de verificação é obrigatório\" ou \"error\":
            \"Formato inválido para o código de verificação\'
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: 'error: \"Não autorizado\'
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: 'error: \"Motorista não encontrado com o código fornecido\'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error: \"Erro ao buscar dados do motorista no Firestore\'
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      tags:
      - Motoristas
  /redefinir-senha/completar:
    post:
      consumes:
      - application/json
      description: Finaliza o processo de redefinição de senha, atualizando-a com
        a nova senha fornecida, utilizando uma chave de reset válida.
      parameters:
      - description: Chave de redefinição e nova senha
        in: body
        name: completar_reset
        required: true
        schema:
          $ref: '#/definitions/handlers.CompletarResetSenhaRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 'message": "Senha redefinida com sucesso!'
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: 'error": "Dados inválidos: {detalhe_do_erro}'
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: 'error": "Chave de reset inválida, já utilizada ou expirada.'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao validar chave de reset." or "error": "Erro
            ao processar informações de segurança." or "error": "Erro ao redefinir
            a senha.'
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Completar Redefinição de Senha
      tags:
      - Redefinir Senha
  /redefinir-senha/iniciar:
    post:
      consumes:
      - application/json
      description: Recebe um email, verifica se está cadastrado e, em caso positivo,
        envia um email com uma chave para redefinição de senha.
      parameters:
      - description: Email para iniciar a redefinição de senha
        in: body
        name: iniciar_reset
        required: true
        schema:
          $ref: '#/definitions/handlers.IniciarResetSenhaRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 'message": "Se o email estiver cadastrado, você receberá instruções
            para redefinir sua senha.'
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: 'error": "Email inválido ou ausente'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao processar a solicitação." or "error": "Erro
            ao enviar email de redefinição de senha.'
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Iniciar Processo de Redefinição de Senha
      tags:
      - Redefinir Senha
  /redefinir-senha/validar:
    post:
      consumes:
      - application/json
      description: Verifica se uma chave de redefinição de senha é válida, não expirou
        e não foi utilizada.
      parameters:
      - description: Chave de redefinição para validação
        in: body
        name: validar_reset
        required: true
        schema:
          $ref: '#/definitions/handlers.ValidarResetSenhaRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 'email": "<EMAIL>", "name": "Nome do Usuario'
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: 'error": "Chave de reset inválida ou ausente'
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: 'error": "Chave de reset inválida, já utilizada ou expirada.'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao validar chave de reset.'
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Validar Chave de Redefinição de Senha
      tags:
      - Redefinir Senha
  /veiculo:
    get:
      description: Retorna uma lista paginada de veículos associados à empresa do
        usuário autenticado.
      parameters:
      - default: 1
        description: Número da página
        in: query
        name: page
        type: integer
      - default: 10
        description: Número de itens por página
        in: query
        maximum: 100
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 'data": []models.Veiculo, "total": int64, "page": int, "limit":
            int'
          schema:
            additionalProperties: true
            type: object
        "400":
          description: 'error": "ID da empresa inválido'
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: 'error": "ID da empresa não encontrado no token'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao buscar veículos'
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Listar Veículos
      tags:
      - Veículos
    post:
      consumes:
      - application/json
      description: Cria um novo registro de veículo associado à empresa do usuário
        autenticado.
      parameters:
      - description: Dados do Veículo para criação
        in: body
        name: veiculo
        required: true
        schema:
          $ref: '#/definitions/models.Veiculo'
      produces:
      - application/json
      responses:
        "201":
          description: Veículo criado com sucesso
          schema:
            $ref: '#/definitions/models.Veiculo'
        "400":
          description: 'error": "ID da empresa inválido" or "error": "Dados inválidos:
            {detalhe_do_erro}'
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: 'error": "ID da empresa não encontrado no token'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao criar veículo'
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Criar Novo Veículo
      tags:
      - Veículos
  /veiculo/{id}:
    delete:
      description: Realiza a exclusão lógica de um veículo (define o campo 'ativo'
        como false), dado seu ID.
      parameters:
      - description: ID do Veículo
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 'message": "Veículo excluído com sucesso'
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: 'error": "ID da empresa inválido" or "error": "ID do veículo
            inválido'
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: 'error": "ID da empresa não encontrado no token'
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: 'error": "Veículo não encontrado ou não pertence à empresa'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao excluir veículo'
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Excluir Veículo (Lógico)
      tags:
      - Veículos
    get:
      description: Retorna os detalhes de um veículo específico, dado seu ID, se pertencer
        à empresa do usuário autenticado.
      parameters:
      - description: ID do Veículo
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Detalhes do Veículo
          schema:
            $ref: '#/definitions/models.Veiculo'
        "400":
          description: 'error": "ID da empresa inválido" or "error": "ID do veículo
            inválido'
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: 'error": "ID da empresa não encontrado no token'
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: 'error": "Veículo não encontrado ou não pertence à empresa'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao buscar veículo'
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Obter Veículo por ID
      tags:
      - Veículos
    patch:
      consumes:
      - application/json
      description: Atualiza parcialmente os dados de um veículo existente, dado seu
        ID. Apenas os campos fornecidos na requisição serão alterados.
      parameters:
      - description: ID do Veículo
        in: path
        name: id
        required: true
        type: string
      - description: Campos do Veículo para atualização parcial. Datas devem ser strings
          no formato RFC3339 (YYYY-MM-DDTHH:MM:SSZ) ou YYYY-MM-DD.
        in: body
        name: veiculo_update
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Veículo atualizado com sucesso
          schema:
            $ref: '#/definitions/models.Veiculo'
        "400":
          description: 'error": "ID da empresa inválido" or "error": "ID do veículo
            inválido" or "error": "Dados inválidos" or "error": "Nenhum campo válido
            para atualização fornecido..." or "error": "Formato de data inválido...'
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: 'error": "ID da empresa não encontrado no token'
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: 'error": "Veículo não encontrado ou não pertence à empresa'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao atualizar veículo'
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Atualizar Parcialmente Veículo (PATCH)
      tags:
      - Veículos
    put:
      consumes:
      - application/json
      description: Atualiza completamente os dados de um veículo existente, dado seu
        ID. Requer que todos os campos do veículo sejam enviados.
      parameters:
      - description: ID do Veículo
        in: path
        name: id
        required: true
        type: string
      - description: Dados completos do Veículo para atualização
        in: body
        name: veiculo
        required: true
        schema:
          $ref: '#/definitions/models.Veiculo'
      produces:
      - application/json
      responses:
        "200":
          description: Veículo atualizado com sucesso
          schema:
            $ref: '#/definitions/models.Veiculo'
        "400":
          description: 'error": "ID da empresa inválido" or "error": "ID do veículo
            inválido" or "error": "Dados inválidos: {detalhe_do_erro}'
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: 'error": "ID da empresa não encontrado no token'
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: 'error": "Veículo não encontrado ou não pertence à empresa'
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 'error": "Erro ao atualizar veículo'
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Atualizar Veículo (PUT)
      tags:
      - Veículos
schemes:
- https
- http
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
