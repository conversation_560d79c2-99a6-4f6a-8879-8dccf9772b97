package handlers

import (
	"context"
	"crypto/tls"
	"fmt"
	"log"
	"net"
	"net/http"
	"net/smtp"
	"os"
	"strings"
	"time"
	"wedriverapigo/models"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.org/x/crypto/bcrypt"
)

type ResetSenhaHandler struct {
	MongoClient *mongo.Client
	DBName      string
}

func NewResetSenhaHandler(client *mongo.Client, dbName string) *ResetSenhaHandler {
	return &ResetSenhaHandler{
		MongoClient: client,
		DBName:      dbName,
	}
}

// Estrutura para o corpo da requisição de iniciar reset de senha
type IniciarResetSenhaRequest struct {
	Email string `json:"email" binding:"required,email"`
}

// @Summary Iniciar Processo de Redefinição de Senha
// @Description Recebe um email, verifica se está cadastrado e, em caso positivo, envia um email com uma chave para redefinição de senha.
// @Tags Redefinir Senha
// @Accept  json
// @Produce  json
// @Param   iniciar_reset body IniciarResetSenhaRequest true "Email para iniciar a redefinição de senha"
// @Success 200 {object} map[string]string "message": "Se o email estiver cadastrado, você receberá instruções para redefinir sua senha."
// @Failure 400 {object} map[string]string "error": "Email inválido ou ausente"
// @Failure 500 {object} map[string]string "error": "Erro ao processar a solicitação." or "error": "Erro ao enviar email de redefinição de senha."
// @Router /redefinir-senha/iniciar [post]
func (h *ResetSenhaHandler) IniciarResetSenha(c *gin.Context) {
	var req IniciarResetSenhaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Email inválido ou ausente"})
		return
	}

	email := strings.ToLower(strings.TrimSpace(req.Email))
	usersCollection := h.MongoClient.Database(h.DBName).Collection("usuarios")
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second) // Aumentar timeout para envio de email
	defer cancel()

	// Verifica se o email existe
	var user models.User
	err := usersCollection.FindOne(ctx, bson.M{"email": email}).Decode(&user)
	if err == mongo.ErrNoDocuments {
		// Por segurança, não informamos se o email existe ou não
		c.JSON(http.StatusOK, gin.H{"message": "Se o email estiver cadastrado, você receberá instruções para redefinir sua senha."})
		return
	}
	if err != nil {
		// Outro erro do MongoDB
		log.Printf("Erro ao verificar email existente: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao processar a solicitação."})
		return
	}

	// Gera chave única para reset de senha
	resetKey := uuid.NewString()

	// Atualiza o usuário com a chave de reset e a data de expiração (1 hora)
	resetExpiry := time.Now().Add(1 * time.Hour)
	_, err = usersCollection.UpdateOne(
		ctx,
		bson.M{"_id": user.ID},
		bson.M{
			"$set": bson.M{
				"reset_key":        resetKey,
				"reset_expiry":     resetExpiry,
				"reset_key_sent":   false,
				"reset_key_used":   false,
				"reset_requested": time.Now(),
			},
		},
	)
	if err != nil {
		log.Printf("Erro ao atualizar usuário com chave de reset: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao processar a solicitação."})
		return
	}

	// --- Envio de Email Real ---
	// Obter configurações de email das variáveis de ambiente
	smtpHost := os.Getenv("SMTP_HOST")
	if smtpHost == "" {
		smtpHost = "srvalertas.weso.com.br" // Valor padrão se não estiver definido no .env
	}
	smtpPort := os.Getenv("SMTP_PORT")
	if smtpPort == "" {
		smtpPort = "587" // Valor padrão se não estiver definido no .env
	}
	smtpUser := os.Getenv("SMTP_USER")
	if smtpUser == "" {
		smtpUser = "<EMAIL>" // Valor padrão se não estiver definido no .env
	}
	smtpPass := os.Getenv("SMTP_PASS")
	if smtpPass == "" {
		smtpPass = "Flrtt@123#2025" // Valor padrão se não estiver definido no .env
	}
	smtpFrom := os.Getenv("SMTP_FROM")
	if smtpFrom == "" {
		smtpFrom = smtpUser // Valor padrão se não estiver definido no .env
	}
	from := smtpFrom
	to := []string{email}

	// Construir a URL de reset usando a URL do frontend das variáveis de ambiente
	frontendURL := os.Getenv("FRONTEND_URL")
	if frontendURL == "" {
		frontendURL = "http://localhost:3000" // Valor padrão se não estiver definido no .env
	}
	resetURL := fmt.Sprintf("%s/redefinir-senha?key=%s", frontendURL, resetKey)

	subject := "Redefinição de Senha - WeDriver"
	body := fmt.Sprintf(`
Olá %s!

Recebemos uma solicitação para redefinir sua senha no WeDriver.
Clique no link abaixo para redefinir sua senha:

%s

Este link expira em 1 hora.

Se você não solicitou a redefinição de senha, por favor ignore este email.

Atenciosamente,
Equipe WeDriver
`, user.Name, resetURL)

	// Montar a mensagem no formato RFC 822
	message := []byte(fmt.Sprintf("To: %s\r\nFrom: %s\r\nSubject: %s\r\n\r\n%s",
		strings.Join(to, ","),
		from,
		subject,
		body))

	// Autenticação
	auth := smtp.PlainAuth("", smtpUser, smtpPass, smtpHost)

	// Enviar email - Usando STARTTLS
	// Conectar ao servidor SMTP
	conn, err := net.Dial("tcp", smtpHost+":"+smtpPort)
	if err != nil {
		log.Printf("ERRO AO CONECTAR AO SERVIDOR SMTP para %s: %v", email, err)
		c.JSON(http.StatusOK, gin.H{"message": "Se o email estiver cadastrado, você receberá instruções para redefinir sua senha."})
		return
	}
	defer conn.Close()

	// Criar cliente SMTP
	client, err := smtp.NewClient(conn, smtpHost)
	if err != nil {
		log.Printf("ERRO AO CRIAR CLIENTE SMTP para %s: %v", email, err)
		c.JSON(http.StatusOK, gin.H{"message": "Se o email estiver cadastrado, você receberá instruções para redefinir sua senha."})
		return
	}
	defer client.Close()

	// Iniciar STARTTLS
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true, // Ignora verificação de certificado - APENAS PARA DESENVOLVIMENTO
		ServerName:         smtpHost,
	}
	if err = client.StartTLS(tlsConfig); err != nil {
		log.Printf("ERRO AO INICIAR STARTTLS para %s: %v", email, err)
		c.JSON(http.StatusOK, gin.H{"message": "Se o email estiver cadastrado, você receberá instruções para redefinir sua senha."})
		return
	}

	// Autenticar
	if err = client.Auth(auth); err != nil {
		log.Printf("ERRO AO AUTENTICAR NO SERVIDOR SMTP para %s: %v", email, err)
		c.JSON(http.StatusOK, gin.H{"message": "Se o email estiver cadastrado, você receberá instruções para redefinir sua senha."})
		return
	}

	// Definir remetente e destinatário
	if err = client.Mail(from); err != nil {
		log.Printf("ERRO AO DEFINIR REMETENTE para %s: %v", email, err)
		c.JSON(http.StatusOK, gin.H{"message": "Se o email estiver cadastrado, você receberá instruções para redefinir sua senha."})
		return
	}

	for _, addr := range to {
		if err = client.Rcpt(addr); err != nil {
			log.Printf("ERRO AO DEFINIR DESTINATÁRIO %s: %v", addr, err)
			c.JSON(http.StatusOK, gin.H{"message": "Se o email estiver cadastrado, você receberá instruções para redefinir sua senha."})
			return
		}
	}

	// Enviar o corpo do email
	w, err := client.Data()
	if err != nil {
		log.Printf("ERRO AO INICIAR ENVIO DE DADOS para %s: %v", email, err)
		c.JSON(http.StatusOK, gin.H{"message": "Se o email estiver cadastrado, você receberá instruções para redefinir sua senha."})
		return
	}

	_, err = w.Write(message)
	if err != nil {
		log.Printf("ERRO AO ESCREVER MENSAGEM para %s: %v", email, err)
		c.JSON(http.StatusOK, gin.H{"message": "Se o email estiver cadastrado, você receberá instruções para redefinir sua senha."})
		return
	}

	err = w.Close()
	if err != nil {
		log.Printf("ERRO AO ENVIAR EMAIL de reset de senha para %s: %v", email, err)
		c.JSON(http.StatusOK, gin.H{"message": "Se o email estiver cadastrado, você receberá instruções para redefinir sua senha."})
		return
	}

	// Atualizar reset_key_sent para true APÓS o envio bem-sucedido
	_, updateErr := usersCollection.UpdateOne(ctx,
		bson.M{"_id": user.ID},
		bson.M{"$set": bson.M{"reset_key_sent": true}},
	)
	if updateErr != nil {
		log.Printf("Erro ao atualizar status de envio de email de reset para %s (ID: %s): %v", email, user.ID.Hex(), updateErr)
		// Logar o erro, mas o email foi enviado, então o processo principal continua.
	} else {
		log.Printf("Email de reset de senha enviado com sucesso para %s", email)
	}
	// --- Fim do Envio de Email ---

	c.JSON(http.StatusOK, gin.H{"message": "Se o email estiver cadastrado, você receberá instruções para redefinir sua senha."})
}

// Estrutura para o corpo da requisição de validar chave de reset
type ValidarResetSenhaRequest struct {
	ResetKey string `json:"reset_key" binding:"required"`
}

// @Summary Validar Chave de Redefinição de Senha
// @Description Verifica se uma chave de redefinição de senha é válida, não expirou e não foi utilizada.
// @Tags Redefinir Senha
// @Accept  json
// @Produce  json
// @Param   validar_reset body ValidarResetSenhaRequest true "Chave de redefinição para validação"
// @Success 200 {object} map[string]string "email": "<EMAIL>", "name": "Nome do Usuario"
// @Failure 400 {object} map[string]string "error": "Chave de reset inválida ou ausente"
// @Failure 404 {object} map[string]string "error": "Chave de reset inválida, já utilizada ou expirada."
// @Failure 500 {object} map[string]string "error": "Erro ao validar chave de reset."
// @Router /redefinir-senha/validar [post]
func (h *ResetSenhaHandler) ValidarResetSenha(c *gin.Context) {
	var req ValidarResetSenhaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Chave de reset inválida ou ausente"})
		return
	}

	usersCollection := h.MongoClient.Database(h.DBName).Collection("usuarios")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Buscar o usuário pela chave de reset, onde reset_expiry > agora e reset_key_used = false
	var user models.User
	err := usersCollection.FindOne(ctx, bson.M{
		"reset_key":      req.ResetKey,
		"reset_expiry":   bson.M{"$gt": time.Now()},
		"reset_key_used": false,
	}).Decode(&user)

	if err == mongo.ErrNoDocuments {
		c.JSON(http.StatusNotFound, gin.H{"error": "Chave de reset inválida, já utilizada ou expirada."})
		return
	}
	if err != nil {
		log.Printf("Erro ao buscar usuário pela chave de reset %s: %v", req.ResetKey, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao validar chave de reset."})
		return
	}

	// Retornar o email do usuário
	c.JSON(http.StatusOK, gin.H{
		"email": user.Email,
		"name":  user.Name,
	})
}

// Estrutura para o corpo da requisição de completar reset de senha
type CompletarResetSenhaRequest struct {
	ResetKey    string `json:"reset_key" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6"` // Exige senha com no mínimo 6 caracteres
}

// @Summary Completar Redefinição de Senha
// @Description Finaliza o processo de redefinição de senha, atualizando-a com a nova senha fornecida, utilizando uma chave de reset válida.
// @Tags Redefinir Senha
// @Accept  json
// @Produce  json
// @Param   completar_reset body CompletarResetSenhaRequest true "Chave de redefinição e nova senha"
// @Success 200 {object} map[string]string "message": "Senha redefinida com sucesso!"
// @Failure 400 {object} map[string]string "error": "Dados inválidos: {detalhe_do_erro}"
// @Failure 404 {object} map[string]string "error": "Chave de reset inválida, já utilizada ou expirada."
// @Failure 500 {object} map[string]string "error": "Erro ao validar chave de reset." or "error": "Erro ao processar informações de segurança." or "error": "Erro ao redefinir a senha."
// @Router /redefinir-senha/completar [post]
func (h *ResetSenhaHandler) CompletarResetSenha(c *gin.Context) {
	var req CompletarResetSenhaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos: " + err.Error()})
		return
	}

	usersCollection := h.MongoClient.Database(h.DBName).Collection("usuarios")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Buscar o usuário pela chave de reset, onde reset_expiry > agora e reset_key_used = false
	var user models.User
	err := usersCollection.FindOne(ctx, bson.M{
		"reset_key":      req.ResetKey,
		"reset_expiry":   bson.M{"$gt": time.Now()},
		"reset_key_used": false,
	}).Decode(&user)

	if err == mongo.ErrNoDocuments {
		c.JSON(http.StatusNotFound, gin.H{"error": "Chave de reset inválida, já utilizada ou expirada."})
		return
	}
	if err != nil {
		log.Printf("Erro ao buscar usuário pela chave de reset %s: %v", req.ResetKey, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao validar chave de reset."})
		return
	}

	// Hash da nova senha
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		log.Printf("Erro ao gerar hash da senha para %s: %v", user.Email, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao processar informações de segurança."})
		return
	}

	// Atualizar a senha do usuário e marcar a chave de reset como utilizada
	resetUsedDate := time.Now()
	update := bson.M{
		"$set": bson.M{
			"hash_pass":       string(hashedPassword),
			"reset_key_used":  true,
			"reset_used_date": resetUsedDate,
			"updated":         time.Now(),
		},
	}

	_, err = usersCollection.UpdateOne(ctx, bson.M{"_id": user.ID}, update)
	if err != nil {
		log.Printf("Erro ao atualizar senha do usuário %s: %v", user.Email, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao redefinir a senha."})
		return
	}

	log.Printf("Senha redefinida com sucesso para o usuário %s", user.Email)
	c.JSON(http.StatusOK, gin.H{"message": "Senha redefinida com sucesso!"})
}
