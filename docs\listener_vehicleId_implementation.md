# Implementação do Campo vehicleId no Listener do Firestore

## 📋 **Resumo da Implementação**

Foi implementado o suporte ao campo `vehicleId` no listener do Firestore que sincroniza dados da collection `scans` do Firebase para a collection `identificacoes` do MongoDB. Esta implementação segue o mesmo padrão já estabelecido para o campo `userMID`.

## 🎯 **Objetivo**

Permitir que o sistema capture e armazene automaticamente o ID do veículo usado em cada identificação, possibilitando:

- **Rastreabilidade completa**: Saber qual motorista (`userMID`) e qual veículo (`vehicleId`) foram usados
- **Auditoria**: Log completo de uso de veículos
- **Relatórios**: Análises por veículo, motorista ou combinação
- **Sincronização automática**: Dados do Firebase são automaticamente mapeados para o MongoDB

## 🔄 **Fluxo de Funcionamento**

### **Processo Completo:**

1. **Firebase → Listener**: Novo scan detectado no Firestore com campo `vehicleId`
2. **Validação**: Verifica se o campo `vehicleId` existe e é uma string válida
3. **Mapeamento**: Campo `vehicleId` do Firebase é mapeado para `vehicleId` no MongoDB
4. **Inserção**: Salva identificação com `vehicleId` no MongoDB
5. **Log**: Log detalhado incluindo informações do veículo

### **Casos Tratados:**

#### **✅ Caso 1: vehicleId Presente**
```json
// Firebase (scans collection)
{
  "userMID": "507f1f77bcf86cd799439011",
  "vehicleId": "507f1f77bcf86cd799439022",
  "placa": "ABC1234",
  "type": "entrada"
}

// MongoDB (identificacoes collection)
{
  "_id": "firestore_doc_id",
  "userMID": "507f1f77bcf86cd799439011",
  "vehicleId": "507f1f77bcf86cd799439022",
  "placa": "ABC1234",
  "type": "entrada",
  "motorista_log": {
    "nome": "João Silva",
    "cpf": "12345678901"
  }
}
```

#### **✅ Caso 2: vehicleId Ausente**
```json
// Firebase (scans collection)
{
  "userMID": "507f1f77bcf86cd799439011",
  "placa": "ABC1234",
  "type": "entrada"
  // vehicleId não presente
}

// MongoDB (identificacoes collection)
{
  "_id": "firestore_doc_id",
  "userMID": "507f1f77bcf86cd799439011",
  // vehicleId não será incluído (omitempty)
  "placa": "ABC1234",
  "type": "entrada",
  "motorista_log": {
    "nome": "João Silva",
    "cpf": "12345678901"
  }
}
```

## 🛠️ **Implementação Técnica**

### **1. Modelo Atualizado**

```go
// models/scan_data_mongo.go
type ScanDataMongoDB struct {
    ID           string            `bson:"_id"`
    UserId       string            `bson:"userMID,omitempty"`
    VehicleId    string            `bson:"vehicleId,omitempty"`     // NOVO CAMPO
    MotoristaLog *MotoristaLogData `bson:"motorista_log,omitempty"`
    // ... outros campos
}
```

### **2. Listener Atualizado**

```go
// listeners/firestore_scans_listener.go
func saveDataToMongoDB(ctx context.Context, mongoClient *mongo.Client, dbName string, targetCollectionName string, firestoreDocID string, data map[string]interface{}) {
    // ... código existente para userMID ...
    
    // Mapear campo vehicleId do Firebase para MongoDB (seguindo padrão do userMID)
    if val, ok := data["vehicleId"]; ok {
        if v, asserted := val.(string); asserted {
            mongoDoc.VehicleId = v
            log.Printf("[MongoDB Sync] Campo vehicleId mapeado: '%s'", v)
        }
    }
    
    // ... resto da implementação ...
}
```

### **3. Logs Aprimorados**

```go
// Log detalhado com informações do motorista e veículo
logParts := []string{fmt.Sprintf("Documento %s inserido com sucesso na coleção '%s'", firestoreDocID, targetCollectionName)}

if mongoDoc.MotoristaLog != nil {
    logParts = append(logParts, fmt.Sprintf("motorista '%s'", mongoDoc.MotoristaLog.Nome))
}

if mongoDoc.VehicleId != "" {
    logParts = append(logParts, fmt.Sprintf("vehicleId '%s'", mongoDoc.VehicleId))
}

log.Printf("[MongoDB Sync] %s", strings.Join(logParts, " com "))
```

## 📊 **Exemplos de Logs**

### **Com motorista e veículo:**
```
[MongoDB Sync] Campo vehicleId mapeado: '507f1f77bcf86cd799439022'
[MongoDB Sync] Buscando dados do motorista '507f1f77bcf86cd799439011' para log de auditoria...
[MongoDB Sync] Log do motorista adicionado: Nome='João Silva', CPF='12345678901'
[MongoDB Sync] Documento firebase_doc_123 inserido com sucesso na coleção 'identificacoes' com motorista 'João Silva' com vehicleId '507f1f77bcf86cd799439022'
```

### **Apenas com motorista:**
```
[MongoDB Sync] Buscando dados do motorista '507f1f77bcf86cd799439011' para log de auditoria...
[MongoDB Sync] Log do motorista adicionado: Nome='João Silva', CPF='12345678901'
[MongoDB Sync] Documento firebase_doc_124 inserido com sucesso na coleção 'identificacoes' com motorista 'João Silva'
```

### **Apenas com veículo:**
```
[MongoDB Sync] Campo vehicleId mapeado: '507f1f77bcf86cd799439022'
[MongoDB Sync] Documento firebase_doc_125 inserido com sucesso na coleção 'identificacoes' com vehicleId '507f1f77bcf86cd799439022'
```

## 🔍 **Validações Implementadas**

- ✅ **Verificação de existência**: Campo `vehicleId` é opcional
- ✅ **Validação de tipo**: Deve ser string
- ✅ **Tratamento gracioso**: Se não existir ou for inválido, é ignorado
- ✅ **Compatibilidade**: Não quebra documentos existentes sem `vehicleId`
- ✅ **Logs detalhados**: Rastreabilidade completa do processo

## 📝 **Documentação Atualizada**

### **Schema do Firebase:**
```json
{
  "vehicleId": {
    "type": "string",
    "description": "ID do veículo no MongoDB (ObjectID) usado na identificação, seguindo o mesmo padrão do userMID"
  }
}
```

### **README atualizado:**
- Adicionado `vehicleId` na lista de campos importantes
- Documentação sobre o propósito do campo
- Exemplos de uso

## ⚠️ **Comportamento com Dados Ausentes**

- Se `vehicleId` não estiver presente no Firebase, o campo não será incluído no MongoDB
- Se `vehicleId` for uma string vazia, o campo não será incluído (devido ao `omitempty`)
- Se `vehicleId` não for uma string, será ignorado e logado como erro
- A funcionalidade é **backward compatible** - documentos antigos sem `vehicleId` continuam funcionando

## 🚀 **Benefícios**

1. **Sincronização Automática**: Campo `vehicleId` é automaticamente mapeado do Firebase para MongoDB
2. **Rastreabilidade Completa**: Agora temos motorista + veículo em cada identificação
3. **Logs Detalhados**: Visibilidade completa do processo de sincronização
4. **Compatibilidade**: Não quebra funcionalidades existentes
5. **Padrão Consistente**: Segue exatamente o mesmo padrão do `userMID`
6. **Performance**: Mapeamento eficiente sem consultas adicionais

## 🔧 **Integração com Endpoints**

Este campo `vehicleId` agora estará disponível automaticamente nos endpoints de identificações, onde será usado para fazer JOIN com a collection `veiculos` e retornar informações completas do veículo (placa, modelo, marca) nas respostas da API.

## ✅ **Status da Implementação**

- ✅ Modelo `ScanDataMongoDB` atualizado
- ✅ Listener do Firestore atualizado
- ✅ Logs aprimorados
- ✅ Documentação do schema atualizada
- ✅ README atualizado
- ✅ Testes de compilação realizados
- ✅ Compatibilidade backward garantida
