# Endpoint: Ativar Motorista

## Descrição
O endpoint `POST /api/v1/motorista/ativar/{id}` permite ativar um motorista que está cadastrado no Firebase, copiando seus dados para o MongoDB e atualizando o Firebase com o ID do MongoDB.

## Rota
```
POST /api/v1/motorista/ativar/{id}
```

## Autenticação
- **Requerida**: Sim
- **Tipo**: <PERSON><PERSON> (JWT)
- **Middleware**: `AuthMiddleware()`

## Parâmetros

### Path Parameters
- `id` (string, obrigatório): ID do motorista no Firebase (Firestore document ID)

### Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## Fluxo de Funcionamento

1. **Validação**: Verifica se o ID foi fornecido
2. **Autenticação**: Extrai a empresa do token JWT
3. **Busca no Firebase**: Localiza o motorista no Firestore pelo ID
4. **Verificação de Ativação**: Confirma se o motorista não foi ativado anteriormente
5. **Mapeamento**: Converte dados do Firestore para modelo MongoDB
6. **Inserção no MongoDB**: Cria o registro no MongoDB com novo ObjectID
7. **Atualização do Firebase**: Adiciona userMID e companyId no Firestore

## Respostas

### Sucesso (200)
```json
{
  "message": "Motorista ativado com sucesso",
  "motorista": {
    "id": "507f1f77bcf86cd799439011",
    "empresa": "507f1f77bcf86cd799439012",
    "nome": "João Silva",
    "cpf": "12345678901",
    "email": "<EMAIL>",
    "telefone": "11999999999",
    "ativo": true,
    "firestore_user_id": "firebase_doc_id",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

### Erro - ID não fornecido (400)
```json
{
  "error": "ID do motorista não fornecido"
}
```

### Erro - Empresa inválida (400)
```json
{
  "error": "ID da empresa inválido"
}
```

### Erro - Não autenticado (401)
```json
{
  "error": "ID da empresa não encontrado no token"
}
```

### Erro - Motorista não encontrado (404)
```json
{
  "error": "Motorista não encontrado no Firebase"
}
```

### Erro - Já ativado (409)
```json
{
  "error": "Motorista já foi ativado anteriormente",
  "userMID": "507f1f77bcf86cd799439011"
}
```

### Erro - CPF obrigatório (400)
```json
{
  "error": "CPF é obrigatório para ativar o motorista"
}
```

### Erro - Interno (500)
```json
{
  "error": "Erro ao salvar motorista no MongoDB"
}
```

## Exemplo de Uso

### cURL
```bash
curl -X POST \
  'https://wedrivergoapi.srv.weso.com.br/api/v1/motorista/ativar/firebase_doc_id_123' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -H 'Content-Type: application/json'
```

### JavaScript (Fetch)
```javascript
const response = await fetch('/api/v1/motorista/ativar/firebase_doc_id_123', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

const result = await response.json();
```

## Campos Mapeados do Firebase para MongoDB

| Firebase | MongoDB | Descrição |
|----------|---------|-----------|
| cpf | cpf | CPF do motorista |
| fullName/name | nome | Nome completo |
| email | email | Email |
| phone | telefone | Telefone |
| status | app_status | Status do app |
| active | ativo | Status ativo |
| typeUser | tipo_usuario | Tipo de usuário |
| verificationCode | codigo_verificacao | Código de verificação |
| birthDate | data_nasc | Data de nascimento |
| avatarURL | foto.url | URL da foto |
| config | config | Configurações |
| permissions | permissions | Permissões |
| modules | modulos | Módulos |
| timezone | timezone | Fuso horário |
| timezoneOffset | timezone_offset | Offset do fuso |

## Campos Adicionados Automaticamente

- `id`: Novo ObjectID do MongoDB
- `empresa`: ID da empresa do usuário autenticado
- `created_at`: Data/hora atual
- `updated_at`: Data/hora atual
- `ativo`: true (sempre ativo ao ativar)
- `firestore_user_id`: ID do documento no Firebase

## Campos Atualizados no Firebase

- `userMID`: ID do MongoDB (ObjectID em hex)
- `companyId`: ID da empresa
- `updatedAt`: Data/hora da ativação
- `status`: "active"

## Logs Gerados

- Sucesso: "Motorista {CPF} ativado com sucesso. MongoDB ID: {id}, Firebase ID: {firebase_id}"
- Firebase atualizado: "Firebase atualizado com sucesso. FirestoreID: {firebase_id}, MongoID: {mongo_id}"
- Avisos: Erros na atualização do Firebase são logados como avisos, mas não falham a operação

## Considerações de Segurança

1. **Isolamento por Empresa**: Motorista é automaticamente associado à empresa do usuário autenticado
2. **Validação de Ativação**: Impede ativação duplicada
3. **Transação Parcial**: Se falhar a atualização do Firebase, o motorista ainda é criado no MongoDB
4. **Logs Detalhados**: Todas as operações são logadas para auditoria
