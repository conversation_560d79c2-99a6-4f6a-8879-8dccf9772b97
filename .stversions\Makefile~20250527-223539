# Variáveis
BINARY_NAME=wedriverapigo
BUILD_DIR=build
DOCKER_IMAGE=wedriverapigo
GO_FILES=$(shell find . -name '*.go' -not -path "./vendor/*")

# Carrega variáveis do arquivo .env
ifneq (,$(wildcard ./.env))
    include .env
    export
endif

# Configurações de Deploy
DEPLOY_USER=root
DEPLOY_HOST=************
DEPLOY_PATH=/root/wedrivergoapi/
DEPLOY_BINARY=$(BINARY_NAME)

# Detecta o sistema operacional e configura o comando SCP
ifeq ($(OS),Windows_NT)
    BINARY_EXTENSION=.exe
    SCP_CMD=scp
    SSHPASS_CMD=sshpass -p "$(DEPLOY_PASSWORD)"
else
    BINARY_EXTENSION=
    SCP_CMD=scp
    SSHPASS_CMD=sshpass -p "$(DEPLOY_PASSWORD)"
endif

.PHONY: all build clean test run docker-build docker-run help docs lint build-all build-linux deploy-linux build-and-deploy

all: build

# Compila o projeto para a plataforma atual
build:
	@echo "Compilando para a plataforma atual..."
	go build -o $(BUILD_DIR)/$(BINARY_NAME)$(BINARY_EXTENSION) main.go

# Compila para todas as plataformas suportadas
build-all:
	@echo "Compilando para todas as plataformas..."
	# Linux AMD64
	GOOS=linux GOARCH=amd64 go build -o $(BUILD_DIR)/linux-amd64/$(BINARY_NAME) main.go
	# Linux ARM64
	GOOS=linux GOARCH=arm64 go build -o $(BUILD_DIR)/linux-arm64/$(BINARY_NAME) main.go
	# Linux ARM
	GOOS=linux GOARCH=arm go build -o $(BUILD_DIR)/linux-arm/$(BINARY_NAME) main.go
	# MacOS AMD64
	GOOS=darwin GOARCH=amd64 go build -o $(BUILD_DIR)/macos-amd64/$(BINARY_NAME) main.go
	# MacOS ARM64
	GOOS=darwin GOARCH=arm64 go build -o $(BUILD_DIR)/macos-arm64/$(BINARY_NAME) main.go
	# Windows AMD64
	GOOS=windows GOARCH=amd64 go build -o $(BUILD_DIR)/windows-amd64/$(BINARY_NAME).exe main.go
	# Windows 386
	GOOS=windows GOARCH=386 go build -o $(BUILD_DIR)/windows-386/$(BINARY_NAME).exe main.go

# Limpa os arquivos de build
clean:
	@echo "Limpando arquivos de build..."
	rm -rf $(BUILD_DIR)
	go clean
	rm -f $(BINARY_NAME)$(BINARY_EXTENSION)

# Executa os testes
test:
	@echo "Executando testes..."
	go test ./... -v

# Executa o programa
run:
	@echo "Executando o programa..."
	go run main.go

# Constrói a imagem Docker
docker-build:
	@echo "Construindo imagem Docker..."
	docker build -t $(DOCKER_IMAGE) .

# Executa o container Docker
docker-run:
	@echo "Executando container Docker..."
	docker run -p 8080:8080 $(DOCKER_IMAGE)

# Gera a documentação Swagger
docs:
	@echo "Gerando documentação Swagger..."
	swag init

# Executa o linter
lint:
	@echo "Executando linter..."
	go vet ./...
	test -z "$$(gofmt -l .)"

# Instala as dependências do projeto
deps:
	@echo "Instalando dependências..."
	go mod download
	go mod tidy

# Atualiza as dependências do projeto
deps-update:
	@echo "Atualizando dependências..."
	go get -u ./...
	go mod tidy
# Compila especificamente para Linux
build-linux:
	@echo "Compilando para Linux..."
	GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -o $(BUILD_DIR)/linux/$(BINARY_NAME) main.go
	@echo "Compilação para Linux concluída: $(BUILD_DIR)/linux/$(BINARY_NAME)"

# Deploy para o servidor Linux
deploy-linux:
	@echo "Fazendo deploy para o servidor Linux..."
	$(SSHPASS_CMD) $(SCP_CMD) $(BUILD_DIR)/linux/$(BINARY_NAME) $(DEPLOY_USER)@$(DEPLOY_HOST):$(DEPLOY_PATH)
	@echo "Deploy concluído!"
# Verifica se sshpass está instalado
check-sshpass:
	@which sshpass > /dev/null || (echo "sshpass não está instalado. Instalando..." && \
	if [ "$(shell uname)" = "Linux" ]; then \
		sudo apt-get update && sudo apt-get install -y sshpass; \
	elif [ "$(shell uname)" = "Darwin" ]; then \
		brew install https://raw.githubusercontent.com/kadwanev/bigboybrew/master/Library/Formula/sshpass.rb; \
	else \
		echo "Por favor, instale sshpass manualmente para seu sistema operacional."; \
		exit 1; \
	fi)
# Compila e faz deploy em um único comando
build-and-deploy: check-sshpass build-linux deploy-linux
	@echo "Build e deploy concluídos com sucesso!"
# Mostra a ajuda
help:
	@echo "Comandos disponíveis:"
	@echo "  make build          - Compila o projeto para a plataforma atual"
	@echo "  make build-all      - Compila para todas as plataformas suportadas"
	@echo "  make build-linux    - Compila especificamente para Linux"
	@echo "  make deploy-linux   - Faz deploy do binário para o servidor Linux"
	@echo "  make build-and-deploy - Compila para Linux e faz deploy em um comando"
	@echo "  make clean          - Remove arquivos de build"
	@echo "  make test           - Executa os testes"
	@echo "  make run           - Executa o programa"
	@echo "  make docker-build   - Constrói a imagem Docker"
	@echo "  make docker-run     - Executa o container Docker"
	@echo "  make docs           - Gera a documentação Swagger"
	@echo "  make lint           - Executa o linter"
	@echo "  make deps           - Instala as dependências"
	@echo "  make deps-update    - Atualiza as dependências"
	@echo "  make help           - Mostra esta mensagem de ajuda"