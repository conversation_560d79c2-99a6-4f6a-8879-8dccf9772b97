package utils

import (
	"reflect"
	"time"

	"github.com/mitchellh/mapstructure"
)

const (
	iso8601DateLayout = "2006-01-02"
)

// StringToTimeHookFuncRFC3339 retorna um DecodeHookFunc que converte strings
// formatadas como RFC3339 ou YYYY-MM-DD para time.Time.
func StringToTimeHookFuncRFC3339() mapstructure.DecodeHookFunc {
	return func(
		f reflect.Type, // Tipo do campo de origem (string)
		t reflect.Type, // Tipo do campo de destino (time.Time)
		data interface{}, // Dados de entrada
	) (interface{}, error) {
		if f.Kind() != reflect.String {
			return data, nil // Não é string, não processa
		}
		if t != reflect.TypeOf(time.Time{}) {
			return data, nil // Não é time.Time, não processa
		}

		str := data.(string)
		if str == "" {
			return time.Time{}, nil // Retorna time.Time zero para string vazia
		}

		// Tenta RFC3339 primeiro (data e hora)
		parsedTime, err := time.Parse(time.RFC3339, str)
		if err == nil {
			// Verifica se é uma data zero (0001-01-01T00:00:00Z)
			if parsedTime.Year() == 1 && parsedTime.Month() == 1 && parsedTime.Day() == 1 {
				return time.Time{}, nil // Retorna time.Time zero para data zero
			}
			return parsedTime, nil
		}

		// Tenta YYYY-MM-DD (apenas data)
		// Se parsear como YYYY-MM-DD, o horário será 00:00:00 UTC
		parsedTime, err = time.Parse(iso8601DateLayout, str)
		if err == nil {
			// Verifica se é uma data zero (0001-01-01)
			if parsedTime.Year() == 1 && parsedTime.Month() == 1 && parsedTime.Day() == 1 {
				return time.Time{}, nil // Retorna time.Time zero para data zero
			}
			return parsedTime, nil
		}

		return data, nil // Deixa o mapstructure tentar outras coisas se esta falhar silenciosamente
	}
}

// StringToTimeHookFuncDateOnly retorna um DecodeHookFunc que converte strings
// formatadas como YYYY-MM-DD para time.Time.
func StringToTimeHookFuncDateOnly() mapstructure.DecodeHookFunc {
	return func(
		f reflect.Type,
		t reflect.Type,
		data interface{},
	) (interface{}, error) {
		if f.Kind() != reflect.String {
			return data, nil
		}
		if t != reflect.TypeOf(time.Time{}) {
			return data, nil
		}

		str := data.(string)
		if str == "" {
			return time.Time{}, nil
		}

		parsedTime, err := time.Parse(iso8601DateLayout, str)
		if err != nil {
			return data, nil
		}

		// Verifica se é uma data zero (0001-01-01)
		if parsedTime.Year() == 1 && parsedTime.Month() == 1 && parsedTime.Day() == 1 {
			return time.Time{}, nil // Retorna time.Time zero para data zero
		}

		return parsedTime, nil
	}
}

// TimeToPointerTimeHookFunc retorna um DecodeHookFunc que converte time.Time para *time.Time,
// tratando datas zero como nil.
func TimeToPointerTimeHookFunc() mapstructure.DecodeHookFunc {
	return func(
		f reflect.Type,
		t reflect.Type,
		data interface{},
	) (interface{}, error) {
		// Verifica se o tipo de origem é time.Time
		if f != reflect.TypeOf(time.Time{}) {
			return data, nil
		}

		// Verifica se o tipo de destino é *time.Time
		if t != reflect.TypeOf((*time.Time)(nil)) {
			return data, nil
		}

		// Converte data para time.Time
		timeValue := data.(time.Time)

		// Verifica se é uma data zero (0001-01-01T00:00:00Z)
		if timeValue.Year() == 1 && timeValue.Month() == 1 && timeValue.Day() == 1 {
			return nil, nil // Retorna nil para data zero
		}

		// Retorna um ponteiro para a data
		return &timeValue, nil
	}
}
