# Schemas do Firebase

Este diretório contém a documentação dos schemas das collections do Firebase utilizadas no projeto WeDriverApiGO.

## Estrutura

Os schemas estão organizados em arquivos JSON separados para cada collection, seguindo o formato JSON Schema (draft-07).

## Collections Documentadas

- `users.json`: Schema da collection de usuários/motoristas
- `scans.json`: Schema da collection de registros de scan (login, logout, checkin, checkout)

## Propósito

Esta documentação serve como referência para:

1. Entender a estrutura de dados no Firebase
2. Garantir consistência ao fazer modificações nas collections
3. Facilitar a integração com outros sistemas
4. Auxiliar novos desenvolvedores a compreender o modelo de dados

## Campos Importantes

### Collection Users

- `userMID`: ID do MongoDB (substitui o antigo campo `userId`)
- `userId`: ID do próprio documento no Firebase (auto-referência)

### Collection Scans

- `userId`: ID do documento do usuário no Firebase
- `type`: Tipo de operação (login, logout, etc.)
- `typeScan`: Método de escaneamento utilizado

## Atualizações

Ao fazer alterações nas collections do Firebase, por favor atualize os schemas correspondentes neste diretório para manter a documentação atualizada.
