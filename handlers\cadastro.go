package handlers

import (
	"context"
	"crypto/tls"
	"fmt" // Adicionar import fmt
	"log"
	"net"
	"net/http"
	"net/smtp" // Adicionar import net/smtp
	"os"
	"strings"
	"time"
	"wedriverapigo/models"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.org/x/crypto/bcrypt" // Import bcrypt
)

type CadastroHandler struct {
	MongoClient *mongo.Client
	DBName      string
}

func NewCadastroHandler(client *mongo.Client, dbName string) *CadastroHandler {
	return &CadastroHandler{
		MongoClient: client,
		DBName:      dbName,
	}
}

// Estrutura para o corpo da requisição de iniciar cadastro
type IniciarCadastroRequest struct {
	Email string `json:"email" binding:"required,email"`
}

// enviarEmailCadastro envia um email de confirmação para o usuário
func (h *CadastroHandler) enviarEmailCadastro(ctx context.Context, c *gin.Context, email string, emailKey string, userID primitive.ObjectID) bool {
	// Obter configurações de email das variáveis de ambiente
	smtpHost := os.Getenv("SMTP_HOST")
	if smtpHost == "" {
		smtpHost = "srvalertas.weso.com.br" // Valor padrão se não estiver definido no .env
	}
	smtpPort := os.Getenv("SMTP_PORT")
	if smtpPort == "" {
		smtpPort = "587" // Valor padrão se não estiver definido no .env
	}
	smtpUser := os.Getenv("SMTP_USER")
	if smtpUser == "" {
		smtpUser = "<EMAIL>" // Valor padrão se não estiver definido no .env
	}
	smtpPass := os.Getenv("SMTP_PASS")
	if smtpPass == "" {
		smtpPass = "Flrtt@123#2025" // Valor padrão se não estiver definido no .env
	}
	smtpFrom := os.Getenv("SMTP_FROM")
	if smtpFrom == "" {
		smtpFrom = smtpUser // Valor padrão se não estiver definido no .env
	}
	from := smtpFrom
	to := []string{email}

	// Construir a URL de confirmação usando a URL do frontend das variáveis de ambiente
	frontendURL := os.Getenv("FRONTEND_URL")
	if frontendURL == "" {
		frontendURL = "http://localhost:3000" // Valor padrão se não estiver definido no .env
	}
	confirmationURL := fmt.Sprintf("%s/completar-cadastro?key=%s", frontendURL, emailKey)

	subject := "Confirme seu cadastro no WeDriver"
	body := fmt.Sprintf(`
Olá!

Obrigado por se cadastrar no WeDriver.
Clique no link abaixo para confirmar seu email e completar seu cadastro:

%s

Se você não iniciou este cadastro, por favor ignore este email.

Atenciosamente,
Equipe WeDriver
`, confirmationURL)

	// Montar a mensagem no formato RFC 822
	message := []byte(fmt.Sprintf("To: %s\r\nFrom: %s\r\nSubject: %s\r\n\r\n%s",
		strings.Join(to, ","),
		from,
		subject,
		body))

	// Autenticação
	auth := smtp.PlainAuth("", smtpUser, smtpPass, smtpHost)

	// Enviar email - Usando STARTTLS
	// Conectar ao servidor SMTP
	conn, err := net.Dial("tcp", smtpHost+":"+smtpPort)
	if err != nil {
		log.Printf("ERRO AO CONECTAR AO SERVIDOR SMTP para %s: %v", email, err)
		if c != nil {
			c.JSON(http.StatusCreated, gin.H{"message": "Processo de cadastro iniciado, mas houve um problema ao enviar o email de confirmação. Tente novamente mais tarde ou contate o suporte."})
		}
		return false
	}
	defer conn.Close()

	// Criar cliente SMTP
	client, err := smtp.NewClient(conn, smtpHost)
	if err != nil {
		log.Printf("ERRO AO CRIAR CLIENTE SMTP para %s: %v", email, err)
		if c != nil {
			c.JSON(http.StatusCreated, gin.H{"message": "Processo de cadastro iniciado, mas houve um problema ao enviar o email de confirmação. Tente novamente mais tarde ou contate o suporte."})
		}
		return false
	}
	defer client.Close()

	// Iniciar STARTTLS
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true, // Ignora verificação de certificado - APENAS PARA DESENVOLVIMENTO
		ServerName:         smtpHost,
	}
	if err = client.StartTLS(tlsConfig); err != nil {
		log.Printf("ERRO AO INICIAR STARTTLS para %s: %v", email, err)
		if c != nil {
			c.JSON(http.StatusCreated, gin.H{"message": "Processo de cadastro iniciado, mas houve um problema ao enviar o email de confirmação. Tente novamente mais tarde ou contate o suporte."})
		}
		return false
	}

	// Autenticar
	if err = client.Auth(auth); err != nil {
		log.Printf("ERRO AO AUTENTICAR NO SERVIDOR SMTP para %s: %v", email, err)
		if c != nil {
			c.JSON(http.StatusCreated, gin.H{"message": "Processo de cadastro iniciado, mas houve um problema ao enviar o email de confirmação. Tente novamente mais tarde ou contate o suporte."})
		}
		return false
	}

	// Definir remetente e destinatário
	if err = client.Mail(from); err != nil {
		log.Printf("ERRO AO DEFINIR REMETENTE para %s: %v", email, err)
		if c != nil {
			c.JSON(http.StatusCreated, gin.H{"message": "Processo de cadastro iniciado, mas houve um problema ao enviar o email de confirmação. Tente novamente mais tarde ou contate o suporte."})
		}
		return false
	}

	for _, addr := range to {
		if err = client.Rcpt(addr); err != nil {
			log.Printf("ERRO AO DEFINIR DESTINATÁRIO %s: %v", addr, err)
			if c != nil {
				c.JSON(http.StatusCreated, gin.H{"message": "Processo de cadastro iniciado, mas houve um problema ao enviar o email de confirmação. Tente novamente mais tarde ou contate o suporte."})
			}
			return false
		}
	}

	// Enviar o corpo do email
	w, err := client.Data()
	if err != nil {
		log.Printf("ERRO AO INICIAR ENVIO DE DADOS para %s: %v", email, err)
		if c != nil {
			c.JSON(http.StatusCreated, gin.H{"message": "Processo de cadastro iniciado, mas houve um problema ao enviar o email de confirmação. Tente novamente mais tarde ou contate o suporte."})
		}
		return false
	}

	_, err = w.Write(message)
	if err != nil {
		log.Printf("ERRO AO ESCREVER MENSAGEM para %s: %v", email, err)
		if c != nil {
			c.JSON(http.StatusCreated, gin.H{"message": "Processo de cadastro iniciado, mas houve um problema ao enviar o email de confirmação. Tente novamente mais tarde ou contate o suporte."})
		}
		return false
	}

	err = w.Close()
	if err != nil {
		log.Printf("ERRO AO ENVIAR EMAIL de confirmação para %s: %v", email, err)
		if c != nil {
			c.JSON(http.StatusCreated, gin.H{"message": "Processo de cadastro iniciado, mas houve um problema ao enviar o email de confirmação. Tente novamente mais tarde ou contate o suporte."})
		}
		return false
	}

	// Atualizar EmailSent para true APÓS o envio bem-sucedido
	usersCollection := h.MongoClient.Database(h.DBName).Collection("usuarios")
	_, updateErr := usersCollection.UpdateOne(ctx,
		bson.M{"_id": userID},
		bson.M{"$set": bson.M{"email_sent": true}},
	)
	if updateErr != nil {
		log.Printf("Erro ao atualizar status de envio de email para %s (ID: %s): %v", email, userID.Hex(), updateErr)
		// Logar o erro, mas o email foi enviado, então o processo principal continua.
	} else {
		log.Printf("Email de confirmação enviado com sucesso para %s", email)
	}

	return true
}

// @Summary Iniciar Processo de Cadastro
// @Description Recebe um email, cria um pré-cadastro para o usuário, gera uma chave de confirmação e envia por email.
// @Tags Cadastro
// @Accept  json
// @Produce  json
// @Param   cadastro body IniciarCadastroRequest true "Email para iniciar o cadastro"
// @Success 201 {object} map[string]string "message": "Processo de cadastro iniciado. Verifique seu email para o código de confirmação."
// @Failure 400 {object} map[string]string "error": "Dados inválidos: {detalhe_do_erro}"
// @Failure 409 {object} map[string]string "error": "Este email já está cadastrado." or "error": "Cadastro pendente para este email..."
// @Failure 500 {object} map[string]string "error": "Erro ao iniciar processo de cadastro."
// @Router /cadastro/iniciar [post]
func (h *CadastroHandler) IniciarCadastro(c *gin.Context) {
	var req IniciarCadastroRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Email inválido ou ausente"})
		return
	}

	email := strings.ToLower(strings.TrimSpace(req.Email))
	usersCollection := h.MongoClient.Database(h.DBName).Collection("usuarios")
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second) // Aumentar timeout para envio de email
	defer cancel()

	// Verifica se o email já existe
	var existingUser models.User
	err := usersCollection.FindOne(ctx, bson.M{"email": email}).Decode(&existingUser)
	if err == nil {
		// Email já existe no banco

		// Verificar se o cadastro já foi completado (email_check = true)
		if existingUser.EmailCheck {
			// Cadastro já foi completado, retornar erro
			log.Printf("Email %s já está cadastrado e ativado.", email)
			c.JSON(http.StatusConflict, gin.H{"error": "Este email já está cadastrado."})
			return
		}

		// Cadastro não foi completado (email_check = false), reenviar email
		log.Printf("Email %s já cadastrado mas não ativado. Gerando nova chave e enviando email novamente.", email)

		// Gerar nova chave de email
		newEmailKey := uuid.NewString()

		// Atualizar o usuário com a nova chave
		_, updateErr := usersCollection.UpdateOne(
			ctx,
			bson.M{"_id": existingUser.ID},
			bson.M{
				"$set": bson.M{
					"email_key":  newEmailKey,
					"email_sent": false,
				},
			},
		)
		if updateErr != nil {
			log.Printf("Erro ao atualizar chave de email para usuário existente %s: %v", email, updateErr)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao processar a solicitação."})
			return
		}

		// Enviar email com a nova chave
		emailSent := h.enviarEmailCadastro(ctx, c, email, newEmailKey, existingUser.ID)
		if !emailSent {
			// O método enviarEmailCadastro já enviou a resposta HTTP em caso de erro
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Email de confirmação reenviado. Verifique seu email para continuar."})
		return
	}
	if err != mongo.ErrNoDocuments {
		// Outro erro do MongoDB
		log.Printf("Erro ao verificar email existente: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao verificar email."})
		return
	}

	// Gera chave única para validação do email
	emailKey := uuid.NewString()

	newUser := models.User{
		ID:         primitive.NewObjectID(),
		Created:    time.Now(),
		Activated:  false,
		Email:      email,
		EmailSent:  false, // Marcar como false inicialmente
		EmailKey:   emailKey,
		EmailCheck: false,
	}

	insertResult, err := usersCollection.InsertOne(ctx, newUser)
	if err != nil {
		log.Printf("Erro ao inserir novo usuário (inicial): %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao iniciar o processo de cadastro."})
		return
	}
	newUserID := insertResult.InsertedID.(primitive.ObjectID) // Obter o ID do usuário inserido

	// Enviar email de confirmação
	emailSent := h.enviarEmailCadastro(ctx, c, email, emailKey, newUserID)
	if !emailSent {
		// O método enviarEmailCadastro já enviou a resposta HTTP em caso de erro
		return
	}

	c.JSON(http.StatusCreated, gin.H{"message": "Processo de cadastro iniciado. Verifique seu email para continuar."})
}

// Estrutura para o corpo da requisição de completar cadastro
type CompletarCadastroRequest struct {
	EmailKey string `json:"email_key" binding:"required"`
	Name     string `json:"name" binding:"required"`
	Password string `json:"password" binding:"required,min=6"` // Exige senha com no mínimo 6 caracteres
}

// Estrutura para o corpo da requisição de validar cadastro
type ValidarCadastroRequest struct {
	EmailKey string `json:"email_key" binding:"required"`
}

// @Summary Validar Chave de Email para Cadastro
// @Description Verifica se uma chave de email (enviada anteriormente) é válida, não expirou e não foi utilizada.
// @Tags Cadastro
// @Accept  json
// @Produce  json
// @Param   validacao body ValidarCadastroRequest true "Chave de email para validação"
// @Success 200 {object} map[string]string "message": "Chave de email válida.", "email": "<EMAIL>"
// @Failure 400 {object} map[string]string "error": "Dados inválidos: {detalhe_do_erro}"
// @Failure 404 {object} map[string]string "error": "Chave de ativação inválida."
// @Failure 409 {object} map[string]string "error": "Este cadastro já foi completado."
// @Failure 410 {object} map[string]string "error": "Chave de ativação expirada."
// @Failure 500 {object} map[string]string "error": "Erro ao validar chave de ativação."
// @Router /cadastro/validar [post]
func (h *CadastroHandler) ValidarCadastro(c *gin.Context) {
	var req ValidarCadastroRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Chave de email inválida ou ausente"})
		return
	}

	usersCollection := h.MongoClient.Database(h.DBName).Collection("usuarios")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Calcular o tempo limite de 1 hora atrás
	umaHoraAtras := time.Now().Add(-1 * time.Hour)

	// Buscar o usuário pela chave, onde created > (agora - 1 hora)
	// Nota: Não verificamos mais se email_check é falso, pois queremos manter a chave válida mesmo após o cadastro ser completado
	var user models.User
	err := usersCollection.FindOne(ctx, bson.M{
		"email_key": req.EmailKey,
		"created":   bson.M{"$gt": umaHoraAtras},
	}).Decode(&user)

	if err == mongo.ErrNoDocuments {
		c.JSON(http.StatusNotFound, gin.H{"error": "Chave de ativação inválida ou expirada."})
		return
	}
	if err != nil {
		log.Printf("Erro ao buscar usuário pela chave %s: %v", req.EmailKey, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao validar chave de ativação."})
		return
	}

	// Retornar o email do usuário e o status do cadastro
	c.JSON(http.StatusOK, gin.H{
		"email":        user.Email,
		"completed":    user.EmailCheck, // Se email_check for true, o cadastro foi completado
		"activated":    user.Activated,  // Se activated for true, o usuário está ativo
		"created_date": user.Created,    // Data de criação do cadastro
	})
}

// @Summary Completar Cadastro de Usuário e Empresa
// @Description Finaliza o processo de cadastro utilizando a chave de email validada, nome e senha. Cria a empresa associada e ativa o usuário.
// @Tags Cadastro
// @Accept  json
// @Produce  json
// @Param   completar_cadastro body CompletarCadastroRequest true "Dados para completar o cadastro"
// @Success 200 {object} map[string]string "message": "Cadastro concluído com sucesso!"
// @Failure 400 {object} map[string]string "error": "Dados inválidos: {detalhe_do_erro}"
// @Failure 404 {object} map[string]string "error": "Chave de ativação inválida."
// @Failure 409 {object} map[string]string "error": "Este cadastro já foi completado anteriormente."
// @Failure 500 {object} map[string]string "error": "Erro ao validar chave de ativação." or "error": "Erro ao configurar a conta da empresa." or "error": "Erro ao processar informações de segurança." or "error": "Erro ao finalizar o cadastro."
// @Router /cadastro/completar [post]
func (h *CadastroHandler) CompletarCadastro(c *gin.Context) {
	var req CompletarCadastroRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos: " + err.Error()})
		return
	}

	usersCollection := h.MongoClient.Database(h.DBName).Collection("usuarios")
	empresasCollection := h.MongoClient.Database(h.DBName).Collection("empresas")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second) // Aumentar timeout para múltiplas operações
	defer cancel()

	// 1. Encontrar o usuário pela chave
	var user models.User
	// Primeiro verificamos se a chave existe
	err := usersCollection.FindOne(ctx, bson.M{"email_key": req.EmailKey}).Decode(&user)
	if err == mongo.ErrNoDocuments {
		c.JSON(http.StatusNotFound, gin.H{"error": "Chave de ativação inválida."})
		return
	}

	// Depois verificamos se o cadastro já foi completado
	if user.EmailCheck {
		c.JSON(http.StatusConflict, gin.H{"error": "Este cadastro já foi completado anteriormente."})
		return
	}
	if err != nil {
		log.Printf("Erro ao buscar usuário pela chave %s: %v", req.EmailKey, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao validar chave de ativação."})
		return
	}

	// 2. Criar a empresa associada (com dados mínimos)
	now := time.Now()
	newEmpresa := models.Empresa{
		ID:           primitive.NewObjectID(),
		Nome:         "Empresa de " + req.Name, // Nome inicial simples
		Email:        user.Email,               // Usa o email do usuário
		Ativo:        true,
		Created:      now,
		Updated:      now,
		EmailContato: user.Email,
		NomeFantasia: "Empresa de " + req.Name, // Pode ser igual ao nome inicialmente
		RazaoSocial:  "Empresa de " + req.Name, // Pode ser igual ao nome inicialmente
	}

	_, err = empresasCollection.InsertOne(ctx, newEmpresa)
	if err != nil {
		log.Printf("Erro ao criar empresa para usuário %s: %v", user.Email, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao configurar a conta da empresa."})
		return
	}

	// 3. Hash da senha
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		log.Printf("Erro ao gerar hash da senha para %s: %v", user.Email, err)
		// É importante não vazar o erro de hash, mas logar internamente.
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao processar informações de segurança."})
		// Considerar remover a empresa criada se o hash falhar? (Transação seria ideal)
		_, delErr := empresasCollection.DeleteOne(ctx, bson.M{"_id": newEmpresa.ID})
		if delErr != nil {
			log.Printf("Falha ao remover empresa órfã %s após erro de hash: %v", newEmpresa.ID.Hex(), delErr)
		}
		return
	}

	// 4. Atualizar o usuário
	checkDate := time.Now()
	update := bson.M{
		"$set": bson.M{
			"name":             req.Name,
			"hash_pass":        string(hashedPassword),
			"empresa":          newEmpresa.ID,
			"activated":        true,
			"email_check":      true,
			"email_check_date": checkDate,
			"role":             []string{"ADMIN"}, // Define o primeiro usuário como ADMIN
			// Mantendo o campo email_key original
		},
	}

	_, err = usersCollection.UpdateOne(ctx, bson.M{"_id": user.ID}, update)
	if err != nil {
		log.Printf("Erro ao atualizar usuário %s após confirmação: %v", user.Email, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao finalizar o cadastro."})
		// Considerar remover a empresa criada se a atualização do usuário falhar?
		_, delErr := empresasCollection.DeleteOne(ctx, bson.M{"_id": newEmpresa.ID})
		if delErr != nil {
			log.Printf("Falha ao remover empresa órfã %s após erro de atualização do usuário: %v", newEmpresa.ID.Hex(), delErr)
		}
		return
	}

	log.Printf("Usuário %s (%s) completou o cadastro e empresa %s criada.", user.Email, req.Name, newEmpresa.ID.Hex())
	c.JSON(http.StatusOK, gin.H{"message": "Cadastro concluído com sucesso!"})
}
