package handlers

import (
	"context"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"
	"wedriverapigo/models"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.org/x/crypto/bcrypt"
)

// Estrutura para o corpo da requisição de login
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// Estrutura para as claims do JWT
type Claims struct {
	UserID    string   `json:"user_id"`
	Email     string   `json:"email"`
	Name      string   `json:"name"`
	Roles     []string `json:"roles"`
	EmpresaID string   `json:"empresa_id"`
	jwt.RegisteredClaims
}

// AuthHandler gerencia as operações de autenticação
type AuthHandler struct {
	MongoClient *mongo.Client
	DBName      string
}

// NewAuthHandler cria um novo handler de autenticação
func NewAuthHandler(client *mongo.Client, dbName string) *AuthHandler {
	return &AuthHandler{
		MongoClient: client,
		DBName:      dbName,
	}
}

// @Summary Login de Usuário
// @Description Autentica um usuário com email e senha e retorna tokens JWT.
// @Tags Autenticação
// @Accept  json
// @Produce  json
// @Param   login body LoginRequest true "Credenciais de Login"
// @Success 200 {object} map[string]interface{} "token": "jwt_token", "refresh_token": "jwt_refresh_token", "expires_at": 1678886400, "user_id": "user_hex_id", "name": "User Name", "roles": ["role1", "role2"], "empresa_id": "empresa_hex_id"
// @Failure 400 {object} map[string]string "error": "Dados inválidos: ..."
// @Failure 401 {object} map[string]string "error": "Credenciais inválidas" or "error": "Usuário não está ativo"
// @Failure 500 {object} map[string]string "error": "Erro ao processar login" or "error": "Erro ao gerar token de autenticação"
// @Router /auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos: " + err.Error()})
		return
	}

	// Normalizar o email
	email := req.Email

	// Buscar o usuário pelo email
	usersCollection := h.MongoClient.Database(h.DBName).Collection("usuarios")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var user models.User
	err := usersCollection.FindOne(ctx, bson.M{"email": email}).Decode(&user)
	if err == mongo.ErrNoDocuments {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Credenciais inválidas"})
		return
	}
	if err != nil {
		log.Printf("Erro ao buscar usuário pelo email %s: %v", email, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao processar login"})
		return
	}

	// Verificar se o usuário está ativo
	if !user.Activated {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não está ativo"})
		return
	}

	// Verificar a senha
	err = bcrypt.CompareHashAndPassword([]byte(user.HashPass), []byte(req.Password))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Credenciais inválidas"})
		return
	}

	// Obter a chave secreta e o tempo de expiração do token
	jwtSecret := os.Getenv("JWT_SECRET")
	if jwtSecret == "" {
		jwtSecret = "wedriver_default_secret_key" // Valor padrão se não estiver definido no .env
	}

	jwtExpirationHoursStr := os.Getenv("JWT_EXPIRATION_HOURS")
	jwtExpirationHours := 24 // Valor padrão: 24 horas
	if jwtExpirationHoursStr != "" {
		var err error
		jwtExpirationHours, err = strconv.Atoi(jwtExpirationHoursStr)
		if err != nil {
			log.Printf("Erro ao converter JWT_EXPIRATION_HOURS para inteiro: %v. Usando valor padrão (24).", err)
		}
	}

	// Configurar o tempo de expiração do refresh token (padrão: 30 dias)
	refreshExpirationDaysStr := os.Getenv("REFRESH_TOKEN_EXPIRATION_DAYS")
	refreshExpirationDays := 30 // Valor padrão: 30 dias
	if refreshExpirationDaysStr != "" {
		var err error
		refreshExpirationDays, err = strconv.Atoi(refreshExpirationDaysStr)
		if err != nil {
			log.Printf("Erro ao converter REFRESH_TOKEN_EXPIRATION_DAYS para inteiro: %v. Usando valor padrão (30).", err)
		}
	}

	// Criar as claims do token
	expirationTime := time.Now().Add(time.Duration(jwtExpirationHours) * time.Hour)
	claims := &Claims{
		UserID:    user.ID.Hex(),
		Email:     user.Email,
		Name:      user.Name,
		Roles:     user.Role,
		EmpresaID: user.EmpresaID.Hex(),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "wedriver-api",
			Subject:   user.ID.Hex(),
		},
	}

	// Criar o token de acesso (access token)
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(jwtSecret))
	if err != nil {
		log.Printf("Erro ao gerar token JWT: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao gerar token de autenticação"})
		return
	}

	// Gerar refresh token (UUID)
	refreshToken := uuid.New().String()
	refreshExpiry := time.Now().Add(time.Duration(refreshExpirationDays) * 24 * time.Hour)

	// Atualizar o usuário com o refresh token
	_, err = usersCollection.UpdateOne(
		ctx,
		bson.M{"_id": user.ID},
		bson.M{
			"$set": bson.M{
				"refresh_token":  refreshToken,
				"refresh_expiry": refreshExpiry,
			},
		},
	)
	if err != nil {
		log.Printf("Erro ao salvar refresh token para o usuário %s: %v", user.Email, err)
		// Continuar mesmo com erro, apenas o access token será retornado
	}

	// Retornar o token e informações básicas do usuário
	c.JSON(http.StatusOK, gin.H{
		"token":         tokenString,
		"expires_at":    expirationTime.Unix(),
		"refresh_token": refreshToken,
		"user": gin.H{
			"id":         user.ID.Hex(),
			"email":      user.Email,
			"name":       user.Name,
			"roles":      user.Role,
			"empresa_id": user.EmpresaID.Hex(),
		},
	})
}

// VerifyToken verifica se um token JWT é válido
func VerifyToken(tokenString string) (*Claims, error) {
	// Obter a chave secreta
	jwtSecret := os.Getenv("JWT_SECRET")
	if jwtSecret == "" {
		jwtSecret = "wedriver_default_secret_key" // Valor padrão se não estiver definido no .env
	}

	// Analisar o token
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(jwtSecret), nil
	})

	if err != nil {
		return nil, err
	}

	// Verificar se o token é válido
	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, jwt.ErrSignatureInvalid
}

// AuthMiddleware é um middleware para verificar se o usuário está autenticado
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Obter o token do cabeçalho Authorization
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Token de autenticação não fornecido"})
			c.Abort()
			return
		}

		// Verificar se o token começa com "Bearer "
		if len(authHeader) < 7 || authHeader[:7] != "Bearer " {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Formato de token inválido"})
			c.Abort()
			return
		}

		// Extrair o token
		tokenString := authHeader[7:]

		// Verificar o token
		claims, err := VerifyToken(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Token inválido ou expirado"})
			c.Abort()
			return
		}

		// Armazenar as claims no contexto para uso posterior
		c.Set("user_id", claims.UserID)
		c.Set("email", claims.Email)
		c.Set("name", claims.Name)
		c.Set("roles", claims.Roles)
		c.Set("empresa_id", claims.EmpresaID)

		c.Next()
	}
}

// Estrutura para o corpo da requisição de refresh token
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// @Summary Refresh de Token JWT
// @Description Gera um novo token de acesso JWT usando um refresh token válido.
// @Tags Autenticação
// @Accept  json
// @Produce  json
// @Param   refresh body RefreshTokenRequest true "Refresh Token"
// @Success 200 {object} map[string]interface{} "token": "new_jwt_token", "expires_at": 1678886400
// @Failure 400 {object} map[string]string "error": "Dados inválidos: ..."
// @Failure 401 {object} map[string]string "error": "Refresh token inválido ou expirado" or "error": "Usuário não está ativo"
// @Failure 500 {object} map[string]string "error": "Erro ao processar refresh token" or "error": "Erro ao gerar token de autenticação"
// @Router /auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos: " + err.Error()})
		return
	}

	// Buscar o usuário pelo refresh token
	usersCollection := h.MongoClient.Database(h.DBName).Collection("usuarios")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var user models.User
	err := usersCollection.FindOne(ctx, bson.M{
		"refresh_token":  req.RefreshToken,
		"refresh_expiry": bson.M{"$gt": time.Now()}, // Verificar se o refresh token não expirou
	}).Decode(&user)

	if err == mongo.ErrNoDocuments {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Refresh token inválido ou expirado"})
		return
	}
	if err != nil {
		log.Printf("Erro ao buscar usuário pelo refresh token: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao processar refresh token"})
		return
	}

	// Verificar se o usuário está ativo
	if !user.Activated {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não está ativo"})
		return
	}

	// Obter a chave secreta e o tempo de expiração do token
	jwtSecret := os.Getenv("JWT_SECRET")
	if jwtSecret == "" {
		jwtSecret = "wedriver_default_secret_key" // Valor padrão se não estiver definido no .env
	}

	jwtExpirationHoursStr := os.Getenv("JWT_EXPIRATION_HOURS")
	jwtExpirationHours := 24 // Valor padrão: 24 horas
	if jwtExpirationHoursStr != "" {
		var err error
		jwtExpirationHours, err = strconv.Atoi(jwtExpirationHoursStr)
		if err != nil {
			log.Printf("Erro ao converter JWT_EXPIRATION_HOURS para inteiro: %v. Usando valor padrão (24).", err)
		}
	}

	// Criar as claims do token
	expirationTime := time.Now().Add(time.Duration(jwtExpirationHours) * time.Hour)
	claims := &Claims{
		UserID:    user.ID.Hex(),
		Email:     user.Email,
		Name:      user.Name,
		Roles:     user.Role,
		EmpresaID: user.EmpresaID.Hex(),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "wedriver-api",
			Subject:   user.ID.Hex(),
		},
	}

	// Criar o token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(jwtSecret))
	if err != nil {
		log.Printf("Erro ao gerar token JWT: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao gerar token de autenticação"})
		return
	}

	// Retornar o novo token
	c.JSON(http.StatusOK, gin.H{
		"token":      tokenString,
		"expires_at": expirationTime.Unix(),
	})
}

// RoleMiddleware é um middleware para verificar se o usuário tem uma determinada role
func RoleMiddleware(requiredRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Obter as roles do contexto
		rolesInterface, exists := c.Get("roles")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
			c.Abort()
			return
		}

		// Converter para []string
		roles, ok := rolesInterface.([]string)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao processar roles do usuário"})
			c.Abort()
			return
		}

		// Verificar se o usuário tem pelo menos uma das roles requeridas
		hasRequiredRole := false
		for _, role := range roles {
			for _, requiredRole := range requiredRoles {
				if role == requiredRole {
					hasRequiredRole = true
					break
				}
			}
			if hasRequiredRole {
				break
			}
		}

		if !hasRequiredRole {
			c.JSON(http.StatusForbidden, gin.H{"error": "Acesso negado: permissões insuficientes"})
			c.Abort()
			return
		}

		c.Next()
	}
}
