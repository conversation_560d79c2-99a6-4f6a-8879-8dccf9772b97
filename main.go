// @title WedriverApiGO API
// @version 1.0
// @description API para gerenciamento e operações da Wedriver.
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host wedrivergoapi.srv.weso.com.br
// @BasePath /api/v1
// @schemes https http

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.
package main

import (
	"context" // Adicionado para o listener
	"log"
	"net/http"
	"os"
	"time"
	"wedriverapigo/config"
	"wedriverapigo/handlers"
	"wedriverapigo/listeners" // Import para o novo listener
	"wedriverapigo/wedriverDatabase"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"

	swaggerFiles "github.com/swaggo/files"     // swagger embed files
	ginSwagger "github.com/swaggo/gin-swagger" // gin-swagger middleware

	_ "wedriverapigo/docs" // Seus documentos gerados pelo swag
)

func main() {
	// Carregar variáveis de ambiente do arquivo .env
	if err := godotenv.Load(); err != nil {
		log.Printf("Aviso: Arquivo .env não encontrado ou não pode ser carregado: %v", err)
	}

	// Inicializa conexão com o banco de dados
	client, ctx, cancel, err := wedriverDatabase.Connect()
	if err != nil {
		log.Fatalf("Erro ao conectar ao MongoDB: %v", err)
	}
	defer func() {
		cancel() // Cancel the context
		if err = client.Disconnect(ctx); err != nil {
			log.Printf("Erro ao desconectar do MongoDB: %v", err)
		}
	}()

	// Inicializa Firebase Firestore e Auth
	firestoreClient, authClient, err := config.InitFirebase()
	if err != nil {
		log.Fatalf("Erro ao inicializar o Firebase: %v", err)
	}
	// Não há necessidade de um defer client.Close() aqui, pois o app Firebase gerencia o ciclo de vida.
	// O cliente Firestore será fechado quando o app Firebase for encerrado, o que geralmente não é explícito em apps de longa duração.

	// Inicializa o Gin
	router := gin.Default()

	// Configurações de Upload de Arquivos
	uploadsDir := os.Getenv("UPLOADS_DIR")
	if uploadsDir == "" {
		uploadsDir = "./uploads_data" // Valor padrão
		log.Printf("UPLOADS_DIR não definido no .env, usando valor padrão: %s", uploadsDir)
	}
	// Garante que o diretório de uploads exista
	if err := os.MkdirAll(uploadsDir, os.ModePerm); err != nil {
		log.Fatalf("Erro ao criar diretório de uploads %s: %v", uploadsDir, err)
	}

	staticRoutePrefix := os.Getenv("STATIC_ROUTE_PREFIX")
	if staticRoutePrefix == "" {
		staticRoutePrefix = "/files" // Valor padrão
		log.Printf("STATIC_ROUTE_PREFIX não definido no .env, usando valor padrão: %s", staticRoutePrefix)
	}
	router.Static(staticRoutePrefix, uploadsDir)
	log.Printf("Servindo arquivos estáticos de '%s' na rota '%s'", uploadsDir, staticRoutePrefix)

	// Configurar CORS para permitir todos os domínios - configuração mais permissiva
	router.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Content-Length", "Accept-Encoding", "X-CSRF-Token", "Authorization", "accept", "origin", "Cache-Control", "X-Requested-With"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	// Obter o nome do banco de dados das variáveis de ambiente
	dbName := os.Getenv("MONGO_DB_NAME")
	if dbName == "" {
		dbName = "WeDriver" // Valor padrão se não estiver definido no .env
	}

	// Grupo de rotas de cadastro
	cadastroRoutes := router.Group("/api/v1/cadastro")
	{
		// Passando o client MongoDB para os handlers
		cadastroHandler := handlers.NewCadastroHandler(client, dbName)

		cadastroRoutes.POST("/iniciar", cadastroHandler.IniciarCadastro)
		cadastroRoutes.POST("/validar", cadastroHandler.ValidarCadastro)
		cadastroRoutes.POST("/completar", cadastroHandler.CompletarCadastro)
	}

	// Grupo de rotas de autenticação
	authRoutes := router.Group("/api/v1/auth")
	{
		// Passando o client MongoDB para os handlers
		authHandler := handlers.NewAuthHandler(client, dbName)

		authRoutes.POST("/login", authHandler.Login)
		authRoutes.POST("/refresh", authHandler.RefreshToken)
	}

	// Grupo de rotas de reset de senha
	resetSenhaRoutes := router.Group("/api/v1/redefinir-senha")
	{
		// Passando o client MongoDB para os handlers
		resetSenhaHandler := handlers.NewResetSenhaHandler(client, dbName)

		resetSenhaRoutes.POST("/iniciar", resetSenhaHandler.IniciarResetSenha)
		resetSenhaRoutes.POST("/validar", resetSenhaHandler.ValidarResetSenha)
		resetSenhaRoutes.POST("/completar", resetSenhaHandler.CompletarResetSenha)
	}

	// Grupo de rotas de motorista (protegidas por autenticação)
	motoristaRoutes := router.Group("/api/v1/motorista")
	motoristaRoutes.Use(handlers.AuthMiddleware())
	{
		// Passando os clients MongoDB, Firestore e Auth para os handlers
		motoristaHandler := handlers.NewMotoristaHandler(client, dbName, firestoreClient, authClient) // Adicionado firestoreClient e authClient

		motoristaRoutes.POST("", motoristaHandler.CreateMotorista)
		motoristaRoutes.GET("", motoristaHandler.GetMotoristas)
		motoristaRoutes.GET("/:id", motoristaHandler.GetMotoristaByID)
		motoristaRoutes.GET("/verificar-codigo", motoristaHandler.GetMotoristaByVerificationCode) // Nova rota para verificar código
		motoristaRoutes.POST("/ativar/:id", motoristaHandler.AtivarMotorista)                     // Nova rota para ativar motorista do Firebase
		motoristaRoutes.PUT("/:id", motoristaHandler.UpdateMotorista)
		motoristaRoutes.PATCH("/:id", motoristaHandler.PatchMotorista) // Nova rota PATCH
		motoristaRoutes.DELETE("/:id", motoristaHandler.DeleteMotorista)
	}

	// Grupo de rotas de veículo (protegidas por autenticação)
	veiculoRoutes := router.Group("/api/v1/veiculo")
	veiculoRoutes.Use(handlers.AuthMiddleware())
	{
		// Passando o client MongoDB para os handlers
		veiculoHandler := handlers.NewVeiculoHandler(client, dbName)

		veiculoRoutes.POST("", veiculoHandler.CreateVeiculo)
		veiculoRoutes.GET("", veiculoHandler.GetVeiculos)
		veiculoRoutes.GET("/:id", veiculoHandler.GetVeiculoByID)
		veiculoRoutes.PUT("/:id", veiculoHandler.UpdateVeiculo)
		veiculoRoutes.PATCH("/:id", veiculoHandler.PatchVeiculo)
		veiculoRoutes.DELETE("/:id", veiculoHandler.DeleteVeiculo)
	}

	// Exemplo de rota protegida (requer autenticação)
	protectedRoutes := router.Group("/api/v1/protected")
	protectedRoutes.Use(handlers.AuthMiddleware())
	{
		// Exemplo de rota que requer autenticação
		protectedRoutes.GET("/user-info", func(c *gin.Context) {
			// Obter informações do usuário do contexto
			userID, _ := c.Get("user_id")
			email, _ := c.Get("email")
			name, _ := c.Get("name")
			roles, _ := c.Get("roles")
			empresaID, _ := c.Get("empresa_id")

			c.JSON(http.StatusOK, gin.H{
				"user_id":    userID,
				"email":      email,
				"name":       name,
				"roles":      roles,
				"empresa_id": empresaID,
			})
		})

		// Exemplo de rota que requer role específica
		adminRoutes := protectedRoutes.Group("/admin")
		adminRoutes.Use(handlers.RoleMiddleware("ADMIN"))
		{
			adminRoutes.GET("/dashboard", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"message": "Bem-vindo ao painel de administração!"})
			})
		}
	}

	// Obter a porta do servidor das variáveis de ambiente
	port := os.Getenv("SERVER_PORT")
	if port == "" {
		port = "8081" // Valor padrão se não estiver definido no .env
	}

	log.Printf("Servidor rodando na porta %s", port)
	// Grupo de rotas de identificação (protegidas por autenticação)
	identificacaoRoutes := router.Group("/api/v1/identificacao")
	identificacaoRoutes.Use(handlers.AuthMiddleware())
	{
		// Passando o client MongoDB para os handlers
		identificacaoHandler := handlers.NewIdentificacaoHandler(client, dbName)

		identificacaoRoutes.GET("", identificacaoHandler.GetIdentificacoes)
		identificacaoRoutes.GET("/:id", identificacaoHandler.GetIdentificacaoByID)
		identificacaoRoutes.GET("/motorista/:motorista_id", identificacaoHandler.GetIdentificacoesByMotorista)
		identificacaoRoutes.GET("/veiculo/:placa", identificacaoHandler.GetIdentificacoesByVeiculo)
		
		// Endpoints de debug removidos
		identificacaoRoutes.GET("/debug", identificacaoHandler.DebugIdentificacaoMotorista)
		identificacaoRoutes.GET("/debug-raw", identificacaoHandler.DebugIdentificacaoRaw)
	}

	// Rota para Swagger UI
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// Inicializar o listener do Firestore para a coleção 'scans' em uma goroutine
	go listeners.GenericFirestoreListener(context.Background(), firestoreClient, dbName, client, "scans", "identificacoes")

	// Inicializar o listener do Firestore para a coleção 'expenses' em uma goroutine
	go listeners.GenericFirestoreListener(context.Background(), firestoreClient, dbName, client, "expenses", "expenses_data") // Assumes 'expenses_data' for MongoDB, confirm if different

	// Inicializar o listener do Firestore para a coleção 'journeys' em uma goroutine
	go listeners.GenericFirestoreListener(context.Background(), firestoreClient, dbName, client, "journeys", "journeys_data") // Assumes 'journeys_data' for MongoDB, confirm if different

	log.Printf("Servidor rodando na porta %s", port)
	log.Printf("Swagger UI disponível em http://localhost:%s/swagger/index.html", port)
	if err := router.Run("0.0.0.0:" + port); err != nil {
		log.Fatalf("Erro ao iniciar o servidor Gin: %v", err)
	}
}
