package handlers

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"time"
	"wedriverapigo/mappers"
	"wedriverapigo/models"
	"wedriverapigo/utils"

	"firebase.google.com/go/v4/auth" // Adicionado para Firebase Authentication
	"github.com/mitchellh/mapstructure"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"cloud.google.com/go/firestore"  // Adicionado para interagir com o Firestore
	"google.golang.org/api/iterator" // Para iterar sobre os resultados do Firestore
)

// FirestoreUserDTO representa os dados de um motorista a serem sincronizados com a coleção 'users' do Firestore.
type FirestoreUserDTO struct {
	CPF              string    `firestore:"cpf,omitempty"`
	FullName         string    `firestore:"fullName,omitempty"`
	Email            string    `firestore:"email,omitempty"`
	Phone            string    `firestore:"phone,omitempty"`
	Status           string    `firestore:"status,omitempty"`
	TypeUser         string    `firestore:"typeUser,omitempty"`
	UserMID          string    `firestore:"userMID,omitempty"` // ID do MongoDB
	UserId           string    `firestore:"userId,omitempty"`  // ID do próprio registro no Firebase
	VerificationCode string    `firestore:"verificationCode,omitempty"`
	BirthDate        time.Time `firestore:"birthDate,omitempty"`
	AvatarURL        string    `firestore:"avatarURL,omitempty"`
	CreatedAt        time.Time `firestore:"createdAt,omitempty"`
	UpdatedAt        time.Time `firestore:"updatedAt,omitempty"`
	// UID            string    `firestore:"uid,omitempty"` // UID do Firebase Auth.
}

// MotoristaHandler gerencia as operações relacionadas a motoristas
type MotoristaHandler struct {
	MongoClient     *mongo.Client
	DBName          string
	FirestoreClient *firestore.Client
	AuthClient      *auth.Client // Adicionado para interagir com Firebase Auth
}

// NewMotoristaHandler cria um novo handler de motoristas
func NewMotoristaHandler(client *mongo.Client, dbName string, firestoreClient *firestore.Client, authClient *auth.Client) *MotoristaHandler {
	return &MotoristaHandler{
		MongoClient:     client,
		DBName:          dbName,
		FirestoreClient: firestoreClient,
		AuthClient:      authClient, // Adicionado
	}
}

// @Summary Criar Novo Motorista
// @Description Cria um novo registro de motorista associado à empresa do usuário autenticado.
// @Tags Motoristas
// @Accept  json
// @Produce  json
// @Param   motorista body models.Motorista true "Dados do Motorista para criação"
// @Success 201 {object} models.Motorista "Motorista criado com sucesso"
// @Failure 400 {object} map[string]string "error": "ID da empresa inválido" or "error": "Dados inválidos: {detalhe_do_erro}"
// @Failure 401 {object} map[string]string "error": "ID da empresa não encontrado no token"
// @Failure 500 {object} map[string]string "error": "Erro ao criar motorista"
// @Security BearerAuth
// @Router /motorista [post]
func (h *MotoristaHandler) CreateMotorista(c *gin.Context) {
	// Obter o ID da empresa do token JWT (armazenado no contexto pelo middleware de autenticação)
	empresaIDStr, exists := c.Get("empresa_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "ID da empresa não encontrado no token"})
		return
	}

	// Converter o ID da empresa para ObjectID
	empresaID, err := primitive.ObjectIDFromHex(empresaIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da empresa inválido"})
		return
	}

	// Vincular o payload JSON a um mapa para inspeção e manipulação flexível
	var payload map[string]interface{}
	if err := c.ShouldBindJSON(&payload); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Payload inválido: " + err.Error()})
		return
	}

	var motorista models.Motorista
	var photoBase64 string

	// Extrair photo Base64 se existir e for string
	if photoPayload, ok := payload["photo"]; ok {
		if pStr, isString := photoPayload.(string); isString {
			photoBase64 = pStr
		}
		delete(payload, "photo") // Remover para não interferir com a decodificação de mapstructure para motorista.Foto (que é FotoMetadata)
	}

	// Usar mapstructure para decodificar o restante do payload em models.Motorista
	// Isso permite que campos como 'birthDate' (string) sejam convertidos para time.Time se houver um hook adequado
	// ou se a struct usar `mapstructure:"birthDate"` e um tipo que o mapstructure possa preencher (ex: string e converter depois).
	// Para datas, é importante garantir que o mapstructure consiga converter ou você precisará de um DecodeHook.
	config := &mapstructure.DecoderConfig{
		Metadata: nil,
		Result:   &motorista,
		TagName:  "json", // Usar a tag json para mapeamento, já que o payload é JSON
		// Adicionar um DecodeHookFunc para converter strings de data para time.Time
		DecodeHook: mapstructure.ComposeDecodeHookFunc(
			utils.StringToTimeHookFuncRFC3339(),  // Tenta converter string para time.Time usando RFC3339
			utils.StringToTimeHookFuncDateOnly(), // Tenta converter string para time.Time usando YYYY-MM-DD
			utils.TimeToPointerTimeHookFunc(),    // Converte time.Time para *time.Time, tratando datas zero como nil
			mapstructure.StringToSliceHookFunc(","),
			mapstructure.StringToTimeDurationHookFunc(),
		),
	}
	decoder, err := mapstructure.NewDecoder(config)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao inicializar decodificador de dados: " + err.Error()})
		return
	}
	if err := decoder.Decode(payload); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos na conversão para motorista: " + err.Error()})
		return
	}

	// Definir os campos obrigatórios e padrão
	now := time.Now().UTC()
	motorista.ID = primitive.NewObjectID() // Gerar novo ID para o novo motorista
	motorista.Empresa = empresaID
	motorista.CreatedAt = now
	motorista.UpdatedAt = now
	if payload["active"] == nil { // Se 'active' não foi enviado, padrão para true
		motorista.Ativo = true
	} // Caso contrário, o valor de 'active' do payload já foi mapeado pelo mapstructure

	// Salvar FotoBase64 (extraída do campo 'photo'), se fornecida
	if photoBase64 != "" {
		uploadsDir := os.Getenv("UPLOADS_DIR")
		if uploadsDir == "" {
			uploadsDir = "./uploads_data" // fallback
			log.Println("UPLOADS_DIR não definido, usando ./uploads_data para motorista")
		}
		subPath := "motoristas/fotos"
		// Usar o novo motorista.ID.Hex() para nomear o arquivo de forma única
		generatedFileName, contentType, extension, errSf := utils.SaveBase64ToFile(photoBase64, uploadsDir, subPath, motorista.ID.Hex())
		if errSf != nil {
			log.Printf("Erro ao salvar foto do motorista %s para empresa %s: %v", motorista.ID.Hex(), empresaID.Hex(), errSf)
			// Considerar se deve retornar erro ou apenas logar
		} else {
			motorista.Foto.FileID = motorista.ID.Hex()
			motorista.Foto.ContentType = contentType
			motorista.Foto.Extension = extension
			motorista.Foto.RelativePath = filepath.ToSlash(filepath.Join(subPath, generatedFileName))
			log.Printf("Foto para motorista %s salva com sucesso.", motorista.ID.Hex())

			// Adicionar esta seção para popular a URL completa
			apiBaseURL := os.Getenv("API_BASE_URL")
			staticRoutePrefix := os.Getenv("STATIC_ROUTE_PREFIX")
			if motorista.Foto.RelativePath != "" { // Somente se o caminho relativo foi definido
				motorista.Foto.URL = utils.CreateFullURL(apiBaseURL, staticRoutePrefix, motorista.Foto.RelativePath)
				log.Printf("URL da foto para motorista %s definida como: %s", motorista.ID.Hex(), motorista.Foto.URL)
			}
			// Fim da seção adicionada
		}
		// Não precisamos mais de motorista.FotoBase64 = ""
	}

	// Inicializar arrays vazios se não foram fornecidos
	if motorista.Modulos == nil {
		motorista.Modulos = map[string]bool{}
	}
	if motorista.Anexos == nil {
		motorista.Anexos = []models.Anexo{}
	}

	// Inserir o motorista no banco de dados
	motoristasCollection := h.MongoClient.Database(h.DBName).Collection("motoristas")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	_, err = motoristasCollection.InsertOne(ctx, motorista)
	if err != nil {
		log.Printf("Erro ao inserir motorista: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao criar motorista"})
		return
	}

	// Construir URL da foto para a resposta
	if motorista.Foto.RelativePath != "" {
		apiBaseURL := os.Getenv("API_BASE_URL")
		staticRoutePrefix := os.Getenv("STATIC_ROUTE_PREFIX")
		motorista.Foto.URL = apiBaseURL + staticRoutePrefix + motorista.Foto.RelativePath
	}

	// Sincronizar com Firestore
	firestoreDocID, syncErr := h.syncMotoristaToFirestore(ctx, &motorista)
	if syncErr != nil {
		// Logar o erro de sincronização, mas considerar se deve falhar a requisição inteira.
		// Por enquanto, vamos logar e continuar, retornando o motorista como criado no MongoDB.
		// Em um cenário ideal, você poderia querer uma transação ou uma lógica de compensação.
		log.Printf("AVISO: Erro ao sincronizar motorista %s (CPF: %s) com Firestore: %v", motorista.ID.Hex(), motorista.CPF, syncErr)
		// Não vamos atualizar o FirestoreUserID no MongoDB se a sincronização falhou.
	} else if firestoreDocID != "" {
		motorista.FirestoreUserID = firestoreDocID
		// Atualizar o motorista no MongoDB com o FirestoreUserID
		update := bson.M{"$set": bson.M{"firestore_user_id": firestoreDocID, "updated_at": time.Now().UTC()}}
		filter := bson.M{"_id": motorista.ID}
		_, updateErr := motoristasCollection.UpdateOne(ctx, filter, update)
		if updateErr != nil {
			log.Printf("AVISO: Erro ao atualizar motorista %s no MongoDB com FirestoreUserID %s: %v", motorista.ID.Hex(), firestoreDocID, updateErr)
			// Mesmo com erro aqui, o motorista foi criado no Mongo e sincronizado no Firestore.
			// O objeto motorista em memória já tem o FirestoreUserID para o retorno JSON, mas não estará no DB.
		} else {
			log.Printf("Motorista %s atualizado no MongoDB com FirestoreUserID: %s", motorista.ID.Hex(), firestoreDocID)
		}
	}

	c.JSON(http.StatusCreated, motorista)
}

// @Summary Listar Motoristas
// @Description Retorna uma lista paginada de motoristas associados à empresa do usuário autenticado.
// @Tags Motoristas
// @Produce  json
// @Param   page query int false "Número da página" default(1)
// @Param   limit query int false "Número de itens por página" default(10) maximum(100)
// @Success 200 {object} map[string]interface{} "data": []models.Motorista, "total": int64, "page": int, "limit": int
// @Failure 400 {object} map[string]string "error": "ID da empresa inválido"
// @Failure 401 {object} map[string]string "error": "ID da empresa não encontrado no token"
// @Failure 500 {object} map[string]string "error": "Erro ao buscar motoristas"
// @Security BearerAuth
// @Router /motorista [get]
func (h *MotoristaHandler) GetMotoristas(c *gin.Context) {
	// Obter o ID da empresa do token JWT
	empresaIDStr, exists := c.Get("empresa_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "ID da empresa não encontrado no token"})
		return
	}

	// Converter o ID da empresa para ObjectID
	empresaID, err := primitive.ObjectIDFromHex(empresaIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da empresa inválido"})
		return
	}

	// Parâmetros de paginação
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if err != nil || limit < 1 {
		limit = 10
	}
	if limit > 100 {
		limit = 100 // Limitar o tamanho máximo da página
	}

	skip := (page - 1) * limit

	// Preparar a consulta
	motoristasCollection := h.MongoClient.Database(h.DBName).Collection("motoristas")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Filtro para buscar apenas motoristas da empresa atual
	filter := bson.M{"empresa": empresaID}

	// Opções de consulta (paginação e ordenação)
	findOptions := options.Find()
	findOptions.SetLimit(int64(limit))
	findOptions.SetSkip(int64(skip))
	findOptions.SetSort(bson.M{"created_at": -1}) // Ordenar por data de criação (mais recente primeiro)

	// Executar a consulta
	cursor, err := motoristasCollection.Find(ctx, filter, findOptions)
	if err != nil {
		log.Printf("Erro ao buscar motoristas: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar motoristas"})
		return
	}
	defer cursor.Close(ctx)

	// Decodificar os resultados
	var motoristas []models.Motorista
	if err := cursor.All(ctx, &motoristas); err != nil {
		log.Printf("Erro ao decodificar motoristas: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao processar dados dos motoristas"})
		return
	}

	// Contar o total de motoristas para a paginação
	total, err := motoristasCollection.CountDocuments(ctx, filter)
	if err != nil {
		log.Printf("Erro ao contar motoristas: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao contar motoristas"})
		return
	}

	// Calcular o total de páginas
	totalPages := (total + int64(limit) - 1) / int64(limit)

	// Construir URLs das fotos para a resposta
	apiBaseURL := os.Getenv("API_BASE_URL")
	staticRoutePrefix := os.Getenv("STATIC_ROUTE_PREFIX")
	for i := range motoristas {
		if motoristas[i].Foto.RelativePath != "" {
			motoristas[i].Foto.URL = apiBaseURL + staticRoutePrefix + "/" + motoristas[i].Foto.RelativePath
		}
	}

	// Retornar os resultados com metadados de paginação
	c.JSON(http.StatusOK, gin.H{
		"data": motoristas,
		"pagination": gin.H{
			"total":       total,
			"page":        page,
			"limit":       limit,
			"total_pages": totalPages,
		},
	})
}

// @Summary Obter Motorista por ID
// @Description Retorna os detalhes de um motorista específico, dado seu ID, se pertencer à empresa do usuário autenticado.
// @Tags Motoristas
// @Produce json
// @Param   id path string true "ID do Motorista"
// @Success 200 {object} models.Motorista "Detalhes do Motorista"
// @Failure 400 {object} map[string]string "error": "ID da empresa inválido" or "error": "ID do motorista inválido"
// @Failure 401 {object} map[string]string "error": "ID da empresa não encontrado no token"
// @Failure 404 {object} map[string]string "error": "Motorista não encontrado ou não pertence à empresa"
// @Failure 500 {object} map[string]string "error": "Erro ao buscar motorista"
// @Security BearerAuth
// @Router /motorista/{id} [get]
func (h *MotoristaHandler) GetMotoristaByID(c *gin.Context) {
	// Obter o ID da empresa do token JWT
	empresaIDStr, exists := c.Get("empresa_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "ID da empresa não encontrado no token"})
		return
	}

	// Converter o ID da empresa para ObjectID
	empresaID, err := primitive.ObjectIDFromHex(empresaIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da empresa inválido"})
		return
	}

	// Obter o ID do motorista da URL
	id := c.Param("id")
	motoristaID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do motorista inválido"})
		return
	}

	// Preparar a consulta
	motoristasCollection := h.MongoClient.Database(h.DBName).Collection("motoristas")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Filtro para buscar o motorista específico da empresa atual
	filter := bson.M{
		"_id":     motoristaID,
		"empresa": empresaID,
	}

	// Executar a consulta
	var motorista models.Motorista
	err = motoristasCollection.FindOne(ctx, filter).Decode(&motorista)
	if err == mongo.ErrNoDocuments {
		c.JSON(http.StatusNotFound, gin.H{"error": "Motorista não encontrado"})
		return
	}
	if err != nil {
		log.Printf("Erro ao buscar motorista: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar motorista"})
		return
	}

	// Construir URL da foto para a resposta
	if motorista.Foto.RelativePath != "" {
		apiBaseURL := os.Getenv("API_BASE_URL")
		staticRoutePrefix := os.Getenv("STATIC_ROUTE_PREFIX")
		motorista.Foto.URL = apiBaseURL + staticRoutePrefix + "/" + motorista.Foto.RelativePath
	}
	c.JSON(http.StatusOK, motorista)
}

// GetMotoristaByVerificationCode @Summary Obter Dados de Pré-Cadastro por Código de Verificação
// @Description Busca os dados de um pré-cadastro de motorista no Firestore usando um código de verificação.
// @Tags Motoristas
// @Produce json
// @Param code query string true "Código de Verificação do Motorista"
// @Success 200 {object} VerifiedMotoristaData "Dados do motorista encontrados"
// @Failure 400 {object} map[string]string "error: \"Código de verificação é obrigatório\" ou \"error\": \"Formato inválido para o código de verificação\""
// @Failure 401 {object} map[string]string "error: \"Não autorizado\""
// @Failure 404 {object} map[string]string "error: \"Motorista não encontrado com o código fornecido\""
// @Failure 500 {object} map[string]string "error: \"Erro ao buscar dados do motorista no Firestore\""
// @Security BearerAuth
// @Router /motorista/verificar-codigo [get]
func (h *MotoristaHandler) GetMotoristaByVerificationCode(c *gin.Context) {
	verificationCode := c.Query("code")
	if verificationCode == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Código de verificação é obrigatório"})
		return
	}

	ctx, cancel := context.WithTimeout(c.Request.Context(), 15*time.Second)
	defer cancel()

	usersCollection := h.FirestoreClient.Collection("users")
	query := usersCollection.Where("verificationCode", "==", verificationCode).Limit(1)

	iter := query.Documents(ctx)
	defer iter.Stop()

	doc, err := iter.Next()
	if err == iterator.Done {
		c.JSON(http.StatusNotFound, gin.H{"error": "Motorista não encontrado com o código fornecido"})
		return
	}
	if err != nil {
		log.Printf("Erro ao buscar motorista por código de verificação '%s' no Firestore: %v", verificationCode, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar dados do motorista no Firestore"})
		return
	}

	firestoreData := doc.Data()
	responseData := MapFirestoreToVerifiedMotorista(doc.Ref.ID, firestoreData)

	c.JSON(http.StatusOK, responseData)
}

// @Summary Atualizar Motorista (PUT)
// @Description Atualiza completamente os dados de um motorista existente, dado seu ID. Requer que todos os campos do motorista sejam enviados.
// @Tags Motoristas
// @Accept  json
// @Produce  json
// @Param   id path string true "ID do Motorista"
// @Param   motorista body models.Motorista true "Dados completos do Motorista para atualização"
// @Success 200 {object} models.Motorista "Motorista atualizado com sucesso"
// @Failure 400 {object} map[string]string "error": "ID da empresa inválido" or "error": "ID do motorista inválido" or "error": "Dados inválidos: {detalhe_do_erro}"
// @Failure 401 {object} map[string]string "error": "ID da empresa não encontrado no token"
// @Failure 404 {object} map[string]string "error": "Motorista não encontrado ou não pertence à empresa"
// @Failure 500 {object} map[string]string "error": "Erro ao atualizar motorista"
// @Security BearerAuth
// @Router /motorista/{id} [put]
func (h *MotoristaHandler) UpdateMotorista(c *gin.Context) {
	// Obter o ID da empresa do token JWT
	empresaIDStr, exists := c.Get("empresa_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "ID da empresa não encontrado no token"})
		return
	}

	// Converter o ID da empresa para ObjectID
	empresaID, err := primitive.ObjectIDFromHex(empresaIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da empresa inválido"})
		return
	}

	// Obter o ID do motorista da URL
	id := c.Param("id")
	motoristaID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do motorista inválido"})
		return
	}

	// Primeiro, buscar o motorista existente para preservar campos imutáveis
	motoristasCollection := h.MongoClient.Database(h.DBName).Collection("motoristas")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel() // Garantir que cancel seja chamado em todos os caminhos de saída

	var existingMotorista models.Motorista
	filter := bson.M{
		"_id":     motoristaID,
		"empresa": empresaID,
	}

	if err := motoristasCollection.FindOne(ctx, filter).Decode(&existingMotorista); err != nil {
		if err == mongo.ErrNoDocuments {
			c.JSON(http.StatusNotFound, gin.H{"error": "Motorista não encontrado ou não pertence à empresa"})
		} else {
			log.Printf("Erro ao buscar motorista existente para PUT: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar motorista"})
		}
		cancel()
		return
	}

	// Ler o payload JSON bruto para um mapa
	var payloadMap map[string]interface{}
	if err := c.ShouldBindJSON(&payloadMap); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Payload inválido para PUT: " + err.Error()})
		cancel()
		return
	}

	var photoBase64StrFromPayload string
	if photoPayload, ok := payloadMap["photo"]; ok {
		if pStr, isString := photoPayload.(string); isString {
			photoBase64StrFromPayload = pStr
		}
		delete(payloadMap, "photo") // Remover antes do mapstructure
	}

	// Inicializar updatedMotoristaData com valores do motorista existente que não devem ser zerados
	// ou que devem ser mantidos se não vierem no payload (comportamento de PUT pode variar aqui).
	// Para um PUT estrito, você começaria com uma struct vazia e aplicaria o payload.
	// Mas como queremos preservar CreatedAt, Empresa, ID, e FirestoreUserID, começamos com eles.
	updatedMotoristaData := models.Motorista{
		ID:              motoristaID, // ID da URL
		Empresa:         empresaID,   // ID da empresa do token
		CreatedAt:       existingMotorista.CreatedAt,
		FirestoreUserID: existingMotorista.FirestoreUserID, // Preservar o FirestoreUserID existente
	}

	// Usar mapstructure para decodificar o payloadMap (sem 'photo') em updatedMotoristaData
	// Isso aplicará os valores do payload sobre os campos de updatedMotoristaData.
	decoderConfig := &mapstructure.DecoderConfig{
		Result:  &updatedMotoristaData, // O resultado é a struct que já tem ID, Empresa, CreatedAt, FirestoreUserID
		TagName: "json",
		DecodeHook: mapstructure.ComposeDecodeHookFunc(
			utils.StringToTimeHookFuncRFC3339(),
			utils.StringToTimeHookFuncDateOnly(),
			utils.TimeToPointerTimeHookFunc(), // Converte time.Time para *time.Time, tratando datas zero como nil
			mapstructure.StringToSliceHookFunc(","),
			mapstructure.StringToTimeDurationHookFunc(),
		),
	}
	decoder, err := mapstructure.NewDecoder(decoderConfig)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao inicializar decodificador de atualização: " + err.Error()})
		cancel()
		return
	}

	if err := decoder.Decode(payloadMap); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Erro ao decodificar dados de atualização: " + err.Error()})
		cancel()
		return
	}

	// Definir campos que são sempre atualizados ou baseados no estado atual
	updatedMotoristaData.UpdatedAt = time.Now().UTC()

	// Lógica para 'ativo': Se 'active' não estava no payloadMap, o valor em updatedMotoristaData.Ativo
	// (que seria 'false' se não estivesse em existingMotorista e não fosse explicitamente copiado)
	// deve ser definido. Para PUT, se não vem, assume-se o valor padrão do tipo (false para bool).
	// Se a intenção é manter existingMotorista.Ativo se 'active' não vier, isso deve ser explícito:
	if _, activePresentInPayload := payloadMap["active"]; !activePresentInPayload {
		updatedMotoristaData.Ativo = existingMotorista.Ativo // Ou defina um padrão se essa for a regra de negócio
	} // Caso contrário, o valor de 'active' do payloadMap já foi aplicado por mapstructure.

	// Processar a foto (Base64 string do payload original ou remoção)
	if photoBase64StrFromPayload != "" {
		uploadsDir := os.Getenv("UPLOADS_DIR")
		if uploadsDir == "" {
			uploadsDir = "./uploads_data" // fallback
			log.Println("UPLOADS_DIR não definido, usando ./uploads_data para motorista PUT")
		}
		// Remover foto antiga, se existir, antes de salvar a nova
		if existingMotorista.Foto.RelativePath != "" {
			oldPhotoFilePath := filepath.Join(uploadsDir, existingMotorista.Foto.RelativePath)
			if err := os.Remove(oldPhotoFilePath); err != nil {
				log.Printf("Falha ao remover foto antiga %s do motorista %s durante PUT: %v", oldPhotoFilePath, motoristaID.Hex(), err)
			}
		}
		subPath := filepath.Join("empresas", empresaID.Hex(), "motoristas", "fotos")
		generatedFileName, contentType, extension, errSf := utils.SaveBase64ToFile(photoBase64StrFromPayload, uploadsDir, subPath, motoristaID.Hex())
		if errSf != nil {
			log.Printf("Erro ao salvar foto durante PUT para motorista %s: %v", motoristaID.Hex(), errSf)
			// Decide se a foto existente deve ser mantida ou removida em caso de erro no upload da nova.
			// Por padrão para PUT, se 'photo' foi fornecido mas falhou, a foto pode ser zerada.
			// Ou manter a existente: updatedMotoristaData.Foto = existingMotorista.Foto
			updatedMotoristaData.Foto = models.FotoMetadata{} // Zera a foto em caso de erro no upload de nova foto
		} else {
			updatedMotoristaData.Foto.FileID = motoristaID.Hex()
			updatedMotoristaData.Foto.ContentType = contentType
			updatedMotoristaData.Foto.Extension = extension
			updatedMotoristaData.Foto.RelativePath = filepath.ToSlash(filepath.Join(subPath, generatedFileName))
			log.Printf("Foto do motorista %s atualizada via PUT.", motoristaID.Hex())
		}
	} else {
		// Se photoBase64StrFromPayload é vazia, significa que:
		// 1. O campo 'photo' não estava no payload.
		// 2. O campo 'photo' estava no payload mas era null ou uma string vazia.
		// 3. O campo 'photo' estava no payload mas não era uma string.
		// Para um PUT, se 'photo' não é explicitamente fornecido para atualização, a foto existente é removida.
		// Remover foto antiga do sistema de arquivos, se existir
		if existingMotorista.Foto.RelativePath != "" {
			uploadsDir := os.Getenv("UPLOADS_DIR") // Obter uploadsDir novamente, pois pode não estar no escopo do bloco anterior
			if uploadsDir == "" {
				uploadsDir = "./uploads_data"
			}
			oldPhotoFilePath := filepath.Join(uploadsDir, existingMotorista.Foto.RelativePath)
			if err := os.Remove(oldPhotoFilePath); err != nil {
				log.Printf("Falha ao remover foto antiga %s do motorista %s durante PUT (remoção explícita): %v", oldPhotoFilePath, motoristaID.Hex(), err)
			} else {
				log.Printf("Foto antiga %s do motorista %s removida com sucesso do sistema de arquivos via PUT.", oldPhotoFilePath, motoristaID.Hex())
			}
		}
		updatedMotoristaData.Foto = models.FotoMetadata{} // Remove a referência da foto no banco de dados
		// O log abaixo é mais específico dependendo da verificação original de 'photoPayload'
		if photoPayload, ok := c.Get("originalPhotoPayload"); ok { // Precisaria salvar o payload original da foto
			if photoPayload == nil {
				log.Printf("Foto do motorista %s removida via PUT (campo 'photo' era nulo no payload).", motoristaID.Hex())
			} else if s, isStr := photoPayload.(string); isStr && s == "" {
				log.Printf("Foto do motorista %s removida via PUT (campo 'photo' era string vazia no payload).", motoristaID.Hex())
			} else {
				log.Printf("Foto do motorista %s removida/não alterada via PUT (campo 'photo' ausente ou tipo inválido no payload).", motoristaID.Hex())
			}
		} else {
			log.Printf("Foto do motorista %s removida/não alterada via PUT (campo 'photo' ausente ou tipo inválido no payload).", motoristaID.Hex())
		}
	}

	// Inicializar arrays/maps vazios se não foram fornecidos e são nil após o bind, para evitar `null` no MongoDB
	if updatedMotoristaData.Modulos == nil {
		updatedMotoristaData.Modulos = map[string]bool{}
	}
	if updatedMotoristaData.Anexos == nil {
		updatedMotoristaData.Anexos = []models.Anexo{}
	}

	// Executar a substituição do documento
	// Usar ReplaceOne para uma operação PUT semântica
	result, err := motoristasCollection.ReplaceOne(ctx, filter, updatedMotoristaData)
	if err != nil {
		log.Printf("Erro ao atualizar motorista: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar motorista"})
		return
	}

	if result.MatchedCount == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Motorista não encontrado"})
		return
	}

	// defer cancel() foi movido para o início, logo após a criação do contexto.

	// Retornar o motorista atualizado
	// Como já temos updatedMotoristaData completo e correto, podemos usá-lo.
	// Ou, se preferir buscar novamente do banco para garantir:
	var motoristaConfirmado models.Motorista
	err = motoristasCollection.FindOne(ctx, filter).Decode(&motoristaConfirmado)
	if err != nil {
		log.Printf("Erro ao buscar motorista após ReplaceOne: %v", err)
		// Mesmo que a busca falhe, a operação de ReplaceOne pode ter sido bem-sucedida
		c.JSON(http.StatusOK, gin.H{"message": "Motorista atualizado com sucesso, mas erro ao retornar os dados completos.", "data": updatedMotoristaData})
		return
	}

	// Construir URL da foto para a resposta
	if motoristaConfirmado.Foto.RelativePath != "" {
		apiBaseURL := os.Getenv("API_BASE_URL")
		staticRoutePrefix := os.Getenv("STATIC_ROUTE_PREFIX")
		if apiBaseURL != "" && staticRoutePrefix != "" {
			motoristaConfirmado.Foto.URL = apiBaseURL + staticRoutePrefix + motoristaConfirmado.Foto.RelativePath
		} else {
			log.Printf("API_BASE_URL ou STATIC_ROUTE_PREFIX não configurados para UpdateMotorista, Foto.URL de %s não será completa", motoristaConfirmado.ID.Hex())
		}
	}

	c.JSON(http.StatusOK, motoristaConfirmado)
}

// @Summary Atualizar Parcialmente Motorista (PATCH)
// @Description Atualiza parcialmente os dados de um motorista existente, dado seu ID. Apenas os campos fornecidos na requisição serão alterados.
// @Tags Motoristas
// @Accept  json
// @Produce  json
// @Param   id path string true "ID do Motorista"
// @Param   motorista_update body map[string]interface{} true "Campos do Motorista para atualização parcial. Datas devem ser strings no formato RFC3339 (YYYY-MM-DDTHH:MM:SSZ) ou YYYY-MM-DD."
// @Success 200 {object} models.Motorista "Motorista atualizado com sucesso"
// @Failure 400 {object} map[string]string "error": "ID da empresa inválido" or "error": "ID do motorista inválido" or "error": "Dados inválidos" or "error": "Nenhum campo válido para atualização fornecido..." or "error": "Formato de data inválido..."
// @Failure 401 {object} map[string]string "error": "ID da empresa não encontrado no token"
// @Failure 404 {object} map[string]string "error": "Motorista não encontrado ou não pertence à empresa"
// @Failure 500 {object} map[string]string "error": "Erro ao atualizar motorista"
// @Security BearerAuth
// @Router /motorista/{id} [patch]
func (h *MotoristaHandler) PatchMotorista(c *gin.Context) {
	// Obter o ID da empresa do token JWT
	empresaIDStr, exists := c.Get("empresa_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "ID da empresa não encontrado no token"})
		return
	}

	// Converter o ID da empresa para ObjectID
	empresaID, err := primitive.ObjectIDFromHex(empresaIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da empresa inválido"})
		return
	}

	// Obter o ID do motorista da URL
	id := c.Param("id")
	motoristaID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do motorista inválido"})
		return
	}

	// Vincular o payload JSON a um mapa para inspeção
	var payload map[string]interface{}
	if err := c.ShouldBindJSON(&payload); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Payload inválido: " + err.Error()})
		return
	}

	// Buscar o motorista existente para obter informações da foto atual, se necessário
	motoristasCollection := h.MongoClient.Database(h.DBName).Collection("motoristas")
	ctxForExisting, cancelForExisting := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancelForExisting()

	var existingMotorista models.Motorista
	filterExisting := bson.M{"_id": motoristaID, "empresa": empresaID}
	if err := motoristasCollection.FindOne(ctxForExisting, filterExisting).Decode(&existingMotorista); err != nil {
		if err == mongo.ErrNoDocuments {
			c.JSON(http.StatusNotFound, gin.H{"error": "Motorista não encontrado ou não pertence à empresa"})
		} else {
			log.Printf("Erro ao buscar motorista existente para PATCH: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar motorista para atualização"})
		}
		return
	}

	updatePayloadForDB := bson.M{}

	// Variável para rastrear se o campo 'photo' foi enviado no payload original
	photoKeyExistsInOriginalPayload := false

	// Processar o campo 'photo' para upload de imagem Base64 ou remoção
	if photoPayload, ok := payload["photo"]; ok {
		photoKeyExistsInOriginalPayload = true
		switch v := photoPayload.(type) {
		case string:
			if v != "" { // String Base64 da foto para upload
				uploadsDir := os.Getenv("UPLOADS_DIR")
				if uploadsDir == "" {
					uploadsDir = "./uploads_data" // fallback
					log.Println("UPLOADS_DIR não definido, usando ./uploads_data para motorista")
				}

				// Remover foto antiga, se existir, antes de salvar a nova
				if existingMotorista.Foto.RelativePath != "" {
					oldPhotoFilePath := filepath.Join(uploadsDir, existingMotorista.Foto.RelativePath)
					if err := os.Remove(oldPhotoFilePath); err != nil {
						log.Printf("Falha ao remover foto antiga %s do motorista %s durante PATCH: %v", oldPhotoFilePath, motoristaID.Hex(), err)
					}
				}

				subPath := filepath.Join("empresas", empresaID.Hex(), "motoristas", "fotos")
				generatedFileName, contentType, extension, errSf := utils.SaveBase64ToFile(v, uploadsDir, subPath, motoristaID.Hex())
				if errSf != nil {
					log.Printf("Erro ao salvar foto Base64 durante PATCH para motorista %s: %v", motoristaID.Hex(), errSf)
					// Considerar retornar http.StatusInternalServerError se o salvamento da foto for crítico
				} else {
					correctedRelativePath := filepath.ToSlash(filepath.Join(subPath, generatedFileName))
					log.Printf("[DEBUG Firestore Sync] Foto do motorista %s atualizada via Base64 no campo 'photo'. RelativePath: %s", motoristaID.Hex(), correctedRelativePath)

					apiBaseURL := os.Getenv("API_BASE_URL")
					staticRoutePrefix := os.Getenv("STATIC_ROUTE_PREFIX")
					fullURL := utils.CreateFullURL(apiBaseURL, staticRoutePrefix, correctedRelativePath)
					log.Printf("[DEBUG Firestore Sync] [ETAPA 1] Foto do motorista %s atualizada via Base64 no campo 'photo'. URL: %s", motoristaID.Hex(), fullURL)

					newFotoMetadata := models.FotoMetadata{
						FileID:       motoristaID.Hex(), // Usando o ID do motorista como base para o nome do arquivo
						ContentType:  contentType,
						Extension:    extension,
						RelativePath: correctedRelativePath,
						URL:          fullURL,
					}
					updatePayloadForDB["foto"] = newFotoMetadata
					log.Printf("[DEBUG Firestore Sync] [ETAPA 2] FotoMetadata criada e adicionada ao updatePayloadForDB. URL: %s", newFotoMetadata.URL) // Atualizar o subdocumento 'foto'
					log.Printf("Foto do motorista %s atualizada via Base64 no campo 'photo'. RelativePath: %s, URL: %s", motoristaID.Hex(), correctedRelativePath, fullURL)
				}
			} else { // String vazia no campo 'photo', indica intenção de remover a foto
				if existingMotorista.Foto.RelativePath != "" {
					uploadsDir := os.Getenv("UPLOADS_DIR")
					if uploadsDir == "" {
						uploadsDir = "./uploads_data"
					}
					oldPhotoFilePath := filepath.Join(uploadsDir, existingMotorista.Foto.RelativePath)
					if err := os.Remove(oldPhotoFilePath); err != nil {
						log.Printf("Falha ao remover foto antiga %s do motorista %s durante PATCH (string vazia): %v", oldPhotoFilePath, motoristaID.Hex(), err)
					} else {
						log.Printf("Foto antiga %s do motorista %s removida do sistema de arquivos via PATCH (string vazia).", oldPhotoFilePath, motoristaID.Hex())
					}
				}
				updatePayloadForDB["foto"] = models.FotoMetadata{} // Limpa os metadados da foto no DB
				log.Printf("Metadados da foto do motorista %s zerados via 'photo': \"\".", motoristaID.Hex())
			}
		case nil: // Se 'photo' for explicitamente null no JSON
			if existingMotorista.Foto.RelativePath != "" {
				uploadsDir := os.Getenv("UPLOADS_DIR")
				if uploadsDir == "" {
					uploadsDir = "./uploads_data"
				}
				oldPhotoFilePath := filepath.Join(uploadsDir, existingMotorista.Foto.RelativePath)
				if err := os.Remove(oldPhotoFilePath); err != nil {
					log.Printf("Falha ao remover foto antiga %s do motorista %s durante PATCH (null): %v", oldPhotoFilePath, motoristaID.Hex(), err)
				} else {
					log.Printf("Foto antiga %s do motorista %s removida do sistema de arquivos via PATCH (null).", oldPhotoFilePath, motoristaID.Hex())
				}
			}
			updatePayloadForDB["foto"] = models.FotoMetadata{} // Limpa os metadados da foto no DB
			log.Printf("Metadados da foto do motorista %s zerados via 'photo': null.", motoristaID.Hex())
		default:
			// O campo 'photo' foi enviado com um tipo inesperado (nem string, nem null)
			// Poderia ser um objeto se o cliente tentasse enviar metadados, o que não é o esperado aqui.
			log.Printf("Campo 'photo' recebido com tipo inesperado (%T) no PATCH para motorista %s. Ignorando.", photoPayload, motoristaID.Hex())
			// Não adicionar ao updatePayloadForDB se o tipo for inesperado, para não sobrescrever com algo inválido.
		}
		delete(payload, "photo") // Remover para não ser processado pelo loop genérico abaixo que mapeia json para bson
	}

	// Usar reflexão para obter os campos do modelo Motorista e suas tags
	motoristaType := reflect.TypeOf(models.Motorista{})
	jsonToBsonMap := make(map[string]string)

	// Mapear tags json para bson
	for i := 0; i < motoristaType.NumField(); i++ {
		field := motoristaType.Field(i)
		jsonTagFull := field.Tag.Get("json")
		bsonTagFull := field.Tag.Get("bson")

		jsonName := jsonTagFull
		if commaIdx := strings.Index(jsonTagFull, ","); commaIdx != -1 {
			jsonName = jsonTagFull[:commaIdx]
		}

		bsonName := bsonTagFull
		if commaIdx := strings.Index(bsonTagFull, ","); commaIdx != -1 {
			bsonName = bsonTagFull[:commaIdx]
		}

		// Usar jsonName puro como chave
		if jsonName != "" && bsonName != "" && bsonName != "_id" && jsonName != "-" {
			jsonToBsonMap[jsonName] = bsonName
		}
	}

	// As adições manuais anteriores para "photo" e "active" devem ser cobertas pela reflexão aprimorada.

	// Iterar sobre os campos do modelo Motorista para construir mapa de campos permitidos
	allowedFields := make(map[string]string) // Mapeia JSON key para BSON key
	dateFields := make(map[string]bool)      // Campos que são do tipo time.Time
	t := reflect.TypeOf(models.Motorista{})
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		jsonTag := field.Tag.Get("json")
		bsonTag := field.Tag.Get("bson")

		if jsonTag == "" || jsonTag == "-" || bsonTag == "-" {
			continue
		}
		jsonKey := strings.Split(jsonTag, ",")[0]
		bsonKey := strings.Split(bsonTag, ",")[0]

		// Campos que não devem ser atualizados diretamente ou já tratados (como 'foto', tratado acima)
		if bsonKey != "_id" && bsonKey != "empresa" && bsonKey != "created_at" && bsonKey != "updated_at" && bsonKey != "ativo" && bsonKey != "foto" {
			allowedFields[jsonKey] = bsonKey
			if field.Type == reflect.TypeOf(time.Time{}) || field.Type == reflect.TypeOf((*time.Time)(nil)) {
				dateFields[jsonKey] = true
			}
		}
	}

	parsedTimeSuccessfully := true

	// Iterar sobre os campos fornecidos no payload
	for key, value := range payload {
		bsonKey, isAllowed := allowedFields[key]
		if !isAllowed {
			log.Printf("Campo '%s' não é permitido para PATCH ou já foi tratado (ex: foto).", key)
			continue // Pular campos não permitidos ou já tratados
		}

		// Se o campo estiver na lista de campos de data, tentar converter para time.Time
		if dateFields[key] {
			if dateStr, ok := value.(string); ok {
				t, err := time.Parse(time.RFC3339, dateStr)
				if err != nil {
					// Tentar parsear como data simples YYYY-MM-DD
					tShort, errShort := time.Parse("2006-01-02", dateStr)
					if errShort != nil {
						log.Printf("Erro ao converter data '%s' para o campo '%s': %v e %v", dateStr, key, err, errShort)
						c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Formato de data inválido para o campo '%s'. Use YYYY-MM-DD ou RFC3339.", key)})
						parsedTimeSuccessfully = false
						break
					}
					updatePayloadForDB[bsonKey] = tShort
				} else {
					updatePayloadForDB[bsonKey] = t
				}
			} else if value == nil {
				// Permitir que campos de data sejam definidos como nulos explicitamente
				updatePayloadForDB[bsonKey] = nil
			} else if _, isTime := value.(time.Time); isTime {
				// Se já for time.Time (improvável via JSON direto, mas para consistência)
				updatePayloadForDB[bsonKey] = value
			} else {
				c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Campo de data '%s' deve ser uma string no formato YYYY-MM-DD ou RFC3339, ou null.", key)})
				parsedTimeSuccessfully = false
				break
			}
		} else if key == "modulos" {
			if modulosMap, ok := value.(map[string]interface{}); ok {
				validModulos := make(map[string]bool)
				for modKey, modVal := range modulosMap {
					if modBool, isBool := modVal.(bool); isBool {
						validModulos[modKey] = modBool
					} else {
						c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Valor inválido para o módulo '%s'. Deve ser booleano.", modKey)})
						parsedTimeSuccessfully = false // Reutilizando a flag para interromper
						break
					}
				}
				if !parsedTimeSuccessfully {
					break
				}
				updatePayloadForDB[bsonKey] = validModulos
			} else if value == nil {
				updatePayloadForDB[bsonKey] = nil // Permitir zerar os módulos
			} else {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Campo 'modulos' deve ser um objeto ou null."})
				parsedTimeSuccessfully = false
				break
			}
		} else {
			// Para outros campos, usar o valor diretamente
			updatePayloadForDB[bsonKey] = value
		}
	}

	if !parsedTimeSuccessfully {
		return
	}

	// Lidar com o campo 'ativo' (active no JSON)
	if activeVal, ok := payload["active"]; ok {
		if activeBool, isBool := activeVal.(bool); isBool {
			updatePayloadForDB["ativo"] = activeBool
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Campo 'active' deve ser booleano."})
			return
		}
	} else {
		// Se 'active' não está no payload, não o alteramos.
	}

	if len(updatePayloadForDB) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Nenhum campo válido para atualização fornecido ou campos não permitidos."})
		return
	}

	updatePayloadForDB["updated_at"] = time.Now().UTC()

	// Preparar a atualização
	// motoristasCollection foi definida anteriormente ao buscar existingMotorista
	ctxUpdate, cancelUpdate := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancelUpdate()

	// Filtro para atualizar apenas o motorista específico da empresa atual
	filter := bson.M{
		"_id":     motoristaID,
		"empresa": empresaID,
	}

	// Executar a atualização
	update := bson.M{"$set": updatePayloadForDB}
	result, err := motoristasCollection.UpdateOne(ctxUpdate, filter, update)
	if err != nil {
		log.Printf("Erro ao aplicar PATCH no motorista: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar motorista"})
		return
	}

	if result.MatchedCount == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Motorista não encontrado ou não pertence à empresa"})
		return
	}

	// Buscar o motorista atualizado para retornar na resposta
	var motoristaAtualizado models.Motorista
	ctxRead, cancelRead := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancelRead()
	err = motoristasCollection.FindOne(ctxRead, filter).Decode(&motoristaAtualizado)
	if err == nil {
		log.Printf("[DEBUG Firestore Sync] [ETAPA 3] Motorista recuperado do MongoDB após atualização. Foto.URL: %s", motoristaAtualizado.Foto.URL)
	}
	if err != nil {
		log.Printf("Erro ao buscar motorista após PATCH: %v", err)
		// Mesmo que não consiga buscar para retornar, a atualização ocorreu.
		// Poderia retornar apenas a mensagem de sucesso ou o objeto antes da busca.
		c.JSON(http.StatusOK, gin.H{"message": "Motorista atualizado com sucesso, mas erro ao retornar os dados completos."})
		return
	}

	// Sincronizar campos específicos com Firestore após o patch bem-sucedido no MongoDB
	fieldsToUpdate := []string{}

	// Iterar sobre o payload original do PATCH para determinar quais campos foram enviados pelo cliente
	if _, ok := payload["name"]; ok {
		fieldsToUpdate = append(fieldsToUpdate, "nome")
	}
	if _, ok := payload["email"]; ok {
		fieldsToUpdate = append(fieldsToUpdate, "email")
	}
	if _, ok := payload["telefone"]; ok {
		fieldsToUpdate = append(fieldsToUpdate, "telefone")
	}
	if _, ok := payload["appStatus"]; ok {
		fieldsToUpdate = append(fieldsToUpdate, "app_status")
	}
	if _, ok := payload["active"]; ok {
		fieldsToUpdate = append(fieldsToUpdate, "ativo")
	}
	if _, ok := payload["data_nascimento"]; ok {
		fieldsToUpdate = append(fieldsToUpdate, "data_nasc")
	}
	if _, ok := payload["tipo_usuario"]; ok {
		fieldsToUpdate = append(fieldsToUpdate, "tipo_usuario")
	}
	if photoKeyExistsInOriginalPayload {
		fieldsToUpdate = append(fieldsToUpdate, "foto")
		log.Printf("[DEBUG Firestore Sync] Chave 'photo' existe no payload original para PATCH? %t", photoKeyExistsInOriginalPayload)
		log.Printf("[DEBUG Firestore Sync] motoristaAtualizado.Foto.URL: '%s'", motoristaAtualizado.Foto.URL)
	}

	// Usar o novo mapeador centralizado
	fieldsToSyncToFirestore := mappers.UpdateSpecificFields(motoristaAtualizado, fieldsToUpdate)

	if len(fieldsToSyncToFirestore) > 0 {
		log.Printf("[DEBUG Firestore Sync] [ETAPA 6] fieldsToSyncToFirestore ANTES da chamada de sincronização real: %+v", fieldsToSyncToFirestore)
		firestoreDocID := motoristaAtualizado.FirestoreUserID
		specificSyncCtx, specificSyncCancel := context.WithTimeout(context.Background(), 15*time.Second)
		defer specificSyncCancel()

		// Se FirestoreUserID não estiver no MongoDB, tentar buscar pelo CPF
		if firestoreDocID == "" && motoristaAtualizado.CPF != "" {
			log.Printf("[PATCH_SYNC] FirestoreUserID não encontrado para motorista %s. Buscando por CPF %s...", motoristaAtualizado.ID.Hex(), motoristaAtualizado.CPF)
			usersCollection := h.FirestoreClient.Collection("users")
			query := usersCollection.Where("cpf", "==", motoristaAtualizado.CPF).Limit(1)
			docs, err := query.Documents(specificSyncCtx).GetAll()
			if err != nil {
				log.Printf("AVISO: Erro ao consultar Firestore por CPF %s para atualização específica: %v", motoristaAtualizado.CPF, err)
			} else if len(docs) > 0 {
				firestoreDocID = docs[0].Ref.ID
				log.Printf("[PATCH_SYNC] Documento Firestore %s encontrado para CPF %s.", firestoreDocID, motoristaAtualizado.CPF)
				// Atualizar o FirestoreUserID no MongoDB se foi encontrado agora
				if motoristaAtualizado.FirestoreUserID != firestoreDocID {
					updateMongoFirestoreID := bson.M{
						"firestore_user_id": firestoreDocID,
						"updated_at":        time.Now().UTC(),
					}
					_, mongoUpdateErr := motoristasCollection.UpdateOne(specificSyncCtx, bson.M{"_id": motoristaAtualizado.ID}, bson.M{"$set": updateMongoFirestoreID})
					if mongoUpdateErr != nil {
						log.Printf("AVISO: Erro ao atualizar FirestoreUserID no MongoDB para motorista %s: %v", motoristaAtualizado.ID.Hex(), mongoUpdateErr)
					} else {
						motoristaAtualizado.FirestoreUserID = firestoreDocID // Atualizar no objeto em memória
						motoristaAtualizado.UpdatedAt = updateMongoFirestoreID["updated_at"].(time.Time)
					}
				}
			} else {
				log.Printf("AVISO: Nenhum documento encontrado no Firestore para CPF %s. Não é possível sincronizar campos específicos. Considere criar o motorista no Firestore primeiro.", motoristaAtualizado.CPF)
			}
		}

		if firestoreDocID != "" {
			err := h.syncSpecificFieldsToFirestore(specificSyncCtx, firestoreDocID, fieldsToSyncToFirestore)
			if err != nil {
				log.Printf("AVISO: Motorista %s atualizado no MongoDB, mas erro ao sincronizar campos específicos com Firestore (DocID: %s): %v", motoristaAtualizado.ID.Hex(), firestoreDocID, err)
			} else {
				// Atualizar o UpdatedAt no objeto motoristaAtualizado para refletir a sincronização, se houver um campo updatedAt nos campos sincronizados.
				if updatedAtFirestore, ok := fieldsToSyncToFirestore["updatedAt"]; ok {
					if updatedAtTime, castOk := updatedAtFirestore.(time.Time); castOk {
						motoristaAtualizado.UpdatedAt = updatedAtTime
					}
				}
			}
		}
	} // Fecha o 'if firestoreDocID != ""'

	c.JSON(http.StatusOK, motoristaAtualizado)
} // Fecha PatchMotorista

func (h *MotoristaHandler) syncSpecificFieldsToFirestore(ctx context.Context, docID string, fieldsToUpdate map[string]interface{}) error {
	if h.FirestoreClient == nil {
		log.Println("SYNC_SPECIFIC ERROR: Firestore client não inicializado no handler.")
		return fmt.Errorf("firestore client não inicializado")
	}
	if docID == "" {
		log.Println("SYNC_SPECIFIC ERROR: docID do Firestore está vazio.")
		return fmt.Errorf("docID do Firestore não pode ser vazio")
	}
	if len(fieldsToUpdate) == 0 {
		log.Println("[SYNC_SPECIFIC] Nenhum campo para atualizar no Firestore.")
		return nil // Não é um erro, apenas nada a fazer
	}

	// Adicionar/atualizar sempre o campo 'updatedAt'
	fieldsToUpdate["updatedAt"] = time.Now() // Use time.Now() para o timestamp atual

	log.Printf("[DEBUG Firestore Sync] fieldsToUpdate ANTES de chamar Set no Firestore: %+v", fieldsToUpdate)

	firestoreCollectionName := "users"
	usersCollection := h.FirestoreClient.Collection(firestoreCollectionName)

	log.Printf("[SYNC_SPECIFIC] Atualizando campos no Firestore. DocID: %s, Dados: %+v", docID, fieldsToUpdate)
	// Usamos Set com MergeAll em um mapa contendo apenas os campos específicos.
	// Isso garante que apenas esses campos sejam tocados no documento do Firestore.
	_, err := usersCollection.Doc(docID).Set(ctx, fieldsToUpdate, firestore.MergeAll)
	if err != nil {
		log.Printf("SYNC_SPECIFIC ERROR: Falha ao atualizar documento %s no Firestore. ERRO: %v. DADOS ENVIADOS: %+v", docID, err, fieldsToUpdate)
		return fmt.Errorf("erro ao atualizar documento %s no Firestore: %w", docID, err)
	}

	log.Printf("[SYNC_SPECIFIC] Documento %s atualizado com sucesso no Firestore com campos específicos.", docID)
	return nil

}

func (h *MotoristaHandler) GetOrCreateFirestoreUser(ctx context.Context, motorista *models.Motorista) (string, error) {
	if h.FirestoreClient == nil {
		log.Println("GET_OR_CREATE_FIRESTORE_USER: Firestore client não inicializado.")
		return "", fmt.Errorf("firestore client não inicializado")
	}
	if motorista == nil || motorista.CPF == "" {
		log.Println("GET_OR_CREATE_FIRESTORE_USER: Motorista ou CPF do motorista é nil/vazio.")
		return "", fmt.Errorf("motorista ou CPF do motorista não pode ser nil/vazio")
	}

	usersCollection := h.FirestoreClient.Collection("users")
	query := usersCollection.Where("cpf", "==", motorista.CPF).Limit(1)

	queryCtx, queryCancel := context.WithTimeout(ctx, 10*time.Second)
	defer queryCancel()

	docs, err := query.Documents(queryCtx).GetAll()
	if err != nil {
		log.Printf("GET_OR_CREATE_FIRESTORE_USER: Erro ao consultar Firestore por CPF %s: %v", motorista.CPF, err)
		return "", fmt.Errorf("erro ao consultar firestore por CPF: %w", err)
	}

	if len(docs) > 0 {
		docID := docs[0].Ref.ID
		log.Printf("GET_OR_CREATE_FIRESTORE_USER: Documento Firestore %s encontrado para CPF %s.", docID, motorista.CPF)
		return docID, nil
	}

	log.Printf("GET_OR_CREATE_FIRESTORE_USER: Nenhum documento encontrado para CPF %s. Criando novo...", motorista.CPF)

	apiBaseURL := os.Getenv("API_BASE_URL")
	staticRoutePrefix := os.Getenv("STATIC_ROUTE_PREFIX")
	avatarURL := ""
	if motorista.Foto.RelativePath != "" {
		avatarURL = apiBaseURL + staticRoutePrefix + motorista.Foto.RelativePath
	} else if motorista.Foto.URL != "" {
		avatarURL = motorista.Foto.URL
	}

	// Gerar um ID temporário para o documento (será o ID real após a criação)
	newDocRef := usersCollection.NewDoc()
	newDocID := newDocRef.ID

	// Usar o novo mapeador centralizado, passando o ID do documento
	firestoreData := mappers.CreateFirestoreUser(motorista, avatarURL, newDocID)

	addCtx, addCancel := context.WithTimeout(ctx, 10*time.Second)
	defer addCancel()

	// Usar o Set em vez de Add para usar o ID que geramos
	_, err = newDocRef.Set(addCtx, firestoreData)
	if err != nil {
		log.Printf("GET_OR_CREATE_FIRESTORE_USER: Erro ao CRIAR novo documento no Firestore para CPF %s: %v. Dados: %+v", motorista.CPF, err, firestoreData)
		return "", fmt.Errorf("erro ao criar novo documento no firestore: %w", err)
	}
	log.Printf("GET_OR_CREATE_FIRESTORE_USER: Novo documento %s CRIADO no Firestore para CPF %s", newDocID, motorista.CPF)
	return newDocRef.ID, nil
}

func (h *MotoristaHandler) syncMotoristaToFirestore(ctx context.Context, motorista *models.Motorista) (string, error) {
	if h.FirestoreClient == nil {
		log.Println("SYNC_MOTORISTA_TO_FIRESTORE: Firestore client não inicializado no handler.")
		return "", fmt.Errorf("firestore client não inicializado")
	}
	if motorista == nil {
		log.Println("SYNC_MOTORISTA_TO_FIRESTORE: Motorista é nil.")
		return "", fmt.Errorf("motorista não pode ser nil")
	}

	firestoreDocID, err := h.GetOrCreateFirestoreUser(ctx, motorista)
	if err != nil {
		log.Printf("SYNC_MOTORISTA_TO_FIRESTORE: Erro ao obter/criar usuário no Firestore para motorista CPF %s: %v", motorista.CPF, err)
		return "", fmt.Errorf("erro ao obter/criar usuário no Firestore: %w", err)
	}

	if firestoreDocID == "" {
		log.Printf("SYNC_MOTORISTA_TO_FIRESTORE: FirestoreDocID está vazio após GetOrCreateFirestoreUser para motorista CPF %s. Isso não deveria acontecer.", motorista.CPF)
		return "", fmt.Errorf("firestoreDocID não pode ser vazio após GetOrCreateFirestoreUser bem-sucedido")
	}

	apiBaseURL := os.Getenv("API_BASE_URL")
	staticRoutePrefix := os.Getenv("STATIC_ROUTE_PREFIX")
	avatarURL := motorista.Foto.URL
	if motorista.Foto.URL == "" && motorista.Foto.RelativePath != "" {
		// Fallback para construir a URL se não estiver presente, usando utils.CreateFullURL
		avatarURL = utils.CreateFullURL(apiBaseURL, staticRoutePrefix, motorista.Foto.RelativePath)
		log.Printf("SYNC_MOTORISTA_TO_FIRESTORE: motorista.Foto.URL estava vazia, URL reconstruída para: %s", avatarURL)
	}

	if h.AuthClient == nil {
		log.Println("SYNC_MOTORISTA_TO_FIRESTORE: Auth client não inicializado no handler.")
		// Retorna firestoreDocID porque pode já ter sido criado por GetOrCreateFirestoreUser
		return firestoreDocID, fmt.Errorf("auth client não inicializado")
	}

	tempPassword, err := utils.GenerateRandomPassword(12) // Gera senha de 12 caracteres
	if err != nil {
		log.Printf("SYNC_MOTORISTA_TO_FIRESTORE: Erro ao gerar senha temporária para motorista CPF %s: %v", motorista.CPF, err)
		return firestoreDocID, fmt.Errorf("erro ao gerar senha temporária: %w", err)
	}

	// Email para Firebase Auth será <EMAIL>
	firebaseAuthEmail := motorista.CPF + "@wedriver.com.br"
	var firebaseAuthUID string

	// Parâmetros para criar o usuário no Firebase Auth
	// Não definimos DisplayName para evitar que o campo 'name' seja automaticamente adicionado ao documento do Firestore
	userToCreateParams := (&auth.UserToCreate{}).
		Email(firebaseAuthEmail).
		Password(tempPassword).
		Disabled(false) // Usuário é habilitado por padrão

	if avatarURL != "" {
		userToCreateParams = userToCreateParams.PhotoURL(avatarURL)
	}

	// Tentar criar usuário no Firebase Auth
	firebaseAuthUser, err := h.AuthClient.CreateUser(ctx, userToCreateParams)
	if err != nil {
		if auth.IsEmailAlreadyExists(err) {
			log.Printf("SYNC_MOTORISTA_TO_FIRESTORE: Usuário com email %s já existe no Firebase Auth. Tentando obter UID.", firebaseAuthEmail)
			fbUser, getErr := h.AuthClient.GetUserByEmail(ctx, firebaseAuthEmail)
			if getErr != nil {
				log.Printf("SYNC_MOTORISTA_TO_FIRESTORE: Erro ao obter usuário existente no Firebase Auth pelo email %s: %v", firebaseAuthEmail, getErr)
				return firestoreDocID, fmt.Errorf("usuário Firebase Auth já existe mas falhou ao obter UID: %w", getErr)
			}
			firebaseAuthUID = fbUser.UID
			log.Printf("SYNC_MOTORISTA_TO_FIRESTORE: UID %s obtido para usuário Firebase Auth existente com email %s.", firebaseAuthUID, firebaseAuthEmail)
		} else {
			log.Printf("SYNC_MOTORISTA_TO_FIRESTORE: Erro ao criar usuário no Firebase Auth para motorista CPF %s (email: %s): %v", motorista.CPF, firebaseAuthEmail, err)
			return firestoreDocID, fmt.Errorf("erro ao criar usuário no Firebase Auth: %w", err)
		}
	} else {
		firebaseAuthUID = firebaseAuthUser.UID
		log.Printf("SYNC_MOTORISTA_TO_FIRESTORE: Usuário criado no Firebase Auth com UID %s para motorista CPF %s (email: %s). Senha temporária: [NÃO REGISTRAR]", firebaseAuthUID, motorista.CPF, firebaseAuthEmail)
	}

	// Atualizar a URL da foto no motorista para garantir que seja usada no mapeamento
	if motorista.Foto.URL == "" && avatarURL != "" {
		motorista.Foto.URL = avatarURL
	}

	// Usar o novo mapeador centralizado
	firestoreData := mappers.MongoToFirestore(motorista, firebaseAuthEmail, firebaseAuthUID, firestoreDocID)

	// Campos como createdAt são definidos em GetOrCreateFirestoreUser e mantidos pelo MergeAll.
	// Campos como rg, genero, endereco, dataNascimento, observacoes, etc., foram removidos
	// por não estarem na estrutura de destino do Firestore especificada.

	log.Printf("[SYNC_MOTORISTA_TO_FIRESTORE DEBUG] Sincronizando motorista %s (Firestore ID: %s) com dados: %+v", motorista.ID.Hex(), firestoreDocID, firestoreData)

	usersCollection := h.FirestoreClient.Collection("users")

	setCtx, setCancel := context.WithTimeout(ctx, 10*time.Second)
	defer setCancel()

	_, err = usersCollection.Doc(firestoreDocID).Set(setCtx, firestoreData, firestore.MergeAll)
	if err != nil {
		log.Printf("SYNC_MOTORISTA_TO_FIRESTORE: Erro ao fazer Set no documento Firestore %s: %v. Dados: %+v", firestoreDocID, err, firestoreData)
		return firestoreDocID, fmt.Errorf("erro ao fazer Set no documento Firestore: %w", err)
	}

	log.Printf("SYNC_MOTORISTA_TO_FIRESTORE: Motorista %s sincronizado com Firestore (ID: %s).", motorista.ID.Hex(), firestoreDocID)
	return firestoreDocID, nil
}

// AtivarMotorista godoc
// @Summary Ativar motorista do Firebase
// @Description Busca um motorista no Firebase pelo ID, copia os dados para o MongoDB e atualiza o Firebase com o ID do MongoDB
// @Tags Motoristas
// @Accept json
// @Produce json
// @Param id path string true "ID do motorista no Firebase"
// @Success 200 {object} models.Motorista "Motorista ativado com sucesso"
// @Failure 400 {object} map[string]string "error": "ID não fornecido"
// @Failure 404 {object} map[string]string "error": "Motorista não encontrado no Firebase"
// @Failure 500 {object} map[string]string "error": "Erro interno do servidor"
// @Security BearerAuth
// @Router /motorista/ativar/{id} [post]
func (h *MotoristaHandler) AtivarMotorista(c *gin.Context) {
	firestoreID := c.Param("id")

	// Validar se o ID foi fornecido
	if firestoreID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do motorista não fornecido"})
		return
	}

	// Obter a empresa do usuário autenticado
	empresaIDInterface, exists := c.Get("empresa_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "ID da empresa não encontrado no token"})
		return
	}

	// O ID da empresa vem como string do middleware de autenticação
	empresaIDStr, ok := empresaIDInterface.(string)
	if !ok {
		log.Printf("Erro ao converter empresa_id para string: %v (tipo: %T)", empresaIDInterface, empresaIDInterface)
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da empresa em formato inválido"})
		return
	}

	// Converter a string para ObjectID
	empresaID, err := primitive.ObjectIDFromHex(empresaIDStr)
	if err != nil {
		log.Printf("Erro ao converter empresa_id string para ObjectID: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da empresa inválido"})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Buscar o motorista no Firebase
	firestoreData, err := h.buscarMotoristaFirestore(ctx, firestoreID)
	if err != nil {
		log.Printf("Erro ao buscar motorista no Firebase: %v", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Motorista não encontrado no Firebase"})
		return
	}

	// Verificar se o motorista já foi ativado (já possui userMID)
	if userMID, exists := firestoreData["userMID"].(string); exists && userMID != "" {
		c.JSON(http.StatusConflict, gin.H{
			"error":   "Motorista já foi ativado anteriormente",
			"userMID": userMID,
		})
		return
	}

	// Converter dados do Firestore para modelo Motorista
	motorista := mappers.FirestoreToMotorista(firestoreID, firestoreData)

	// Definir campos obrigatórios
	now := time.Now().UTC()
	motorista.ID = primitive.NewObjectID() // Gerar novo ID MongoDB
	motorista.Empresa = empresaID          // Associar à empresa do usuário autenticado
	motorista.CreatedAt = now
	motorista.UpdatedAt = now
	motorista.ActivatedAt = &now
	motorista.Ativo = true
	motorista.AppStatus = "create_password" // Definir o status para criar senha

	// Verificar se CPF foi fornecido
	if motorista.CPF == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "CPF é obrigatório para ativar o motorista"})
		return
	}

	// Inserir no MongoDB
	motoristaCollection := h.MongoClient.Database(h.DBName).Collection("motoristas")
	_, err = motoristaCollection.InsertOne(ctx, motorista)
	if err != nil {
		log.Printf("Erro ao inserir motorista no MongoDB: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao salvar motorista no MongoDB"})
		return
	}

	// Atualizar Firebase com o ID do MongoDB
	err = h.atualizarFirebaseComMongoID(ctx, firestoreID, motorista.ID.Hex(), empresaID.Hex())
	if err != nil {
		log.Printf("AVISO: Erro ao atualizar Firebase com ID do MongoDB: %v", err)
		// Não falha a operação, apenas loga o aviso
	}

	log.Printf("Motorista %s ativado com sucesso. MongoDB ID: %s, Firebase ID: %s",
		motorista.CPF, motorista.ID.Hex(), firestoreID)

	c.JSON(http.StatusOK, gin.H{
		"message":   "Motorista ativado com sucesso",
		"motorista": motorista,
	})
}

// buscarMotoristaFirestore busca um motorista no Firestore pelo ID
func (h *MotoristaHandler) buscarMotoristaFirestore(ctx context.Context, firestoreID string) (map[string]interface{}, error) {
	if h.FirestoreClient == nil {
		return nil, fmt.Errorf("firestore client não inicializado")
	}

	usersCollection := h.FirestoreClient.Collection("users")
	doc, err := usersCollection.Doc(firestoreID).Get(ctx)
	if err != nil {
		return nil, fmt.Errorf("erro ao buscar documento no Firestore: %w", err)
	}

	if !doc.Exists() {
		return nil, fmt.Errorf("documento não encontrado no Firestore")
	}

	return doc.Data(), nil
}

// atualizarFirebaseComMongoID atualiza o documento do Firebase com o ID do MongoDB
func (h *MotoristaHandler) atualizarFirebaseComMongoID(ctx context.Context, firestoreID, mongoID, empresaID string) error {
	if h.FirestoreClient == nil {
		return fmt.Errorf("firestore client não inicializado")
	}

	usersCollection := h.FirestoreClient.Collection("users")
	updateData := map[string]interface{}{
		"userMID":   mongoID,
		"companyId": empresaID,
		"updatedAt": time.Now().UTC(),
		"status":    "create_password", // Status para solicitar criação de senha
	}

	_, err := usersCollection.Doc(firestoreID).Set(ctx, updateData, firestore.MergeAll)
	if err != nil {
		return fmt.Errorf("erro ao atualizar documento no Firestore: %w", err)
	}

	log.Printf("Firebase atualizado com sucesso. FirestoreID: %s, MongoID: %s", firestoreID, mongoID)
	return nil
}

func (h *MotoristaHandler) DeleteMotorista(c *gin.Context) {
	// Obter o ID da empresa do token JWT
	empresaIDStr, exists := c.Get("empresa_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "ID da empresa não encontrado no token"})
		return
	}

	// Converter o ID da empresa para ObjectID
	empresaID, err := primitive.ObjectIDFromHex(empresaIDStr.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da empresa inválido"})
		return
	}

	// Obter o ID do motorista da URL
	id := c.Param("id")
	motoristaID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do motorista inválido"})
		return
	}

	// Preparar a atualização
	motoristasCollection := h.MongoClient.Database(h.DBName).Collection("motoristas")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Filtro para atualizar apenas o motorista específico da empresa atual
	filter := bson.M{
		"_id":     motoristaID,
		"empresa": empresaID,
	}

	// Executar a exclusão lógica (definir ativo = false)
	update := bson.M{
		"$set": bson.M{
			"ativo":      false,
			"updated_at": time.Now().UTC(),
		},
	}

	result, err := motoristasCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		log.Printf("Erro ao excluir motorista: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao excluir motorista"})
		return
	}

	if result.MatchedCount == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Motorista não encontrado"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Motorista excluído com sucesso"})
}
