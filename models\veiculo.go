package models

import (
	"encoding/json"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Veiculo representa um veículo no sistema
type Veiculo struct {
	ID        primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`
	Empresa   primitive.ObjectID `bson:"empresa" json:"empresa"` // Associado à empresa do token
	CreatedAt time.Time          `bson:"created_at" json:"created_at"`
	UpdatedAt time.Time          `bson:"updated_at" json:"updated_at"`
	Ativo     bool               `bson:"ativo" json:"active"`

	Placa               string       `bson:"placa" json:"plate"`
	Modelo              string       `bson:"modelo" json:"model"`
	Marca               string       `bson:"marca" json:"brand"`
	Ano                 string       `bson:"ano" json:"year"`
	Cor                 string       `bson:"cor" json:"color"`
	Tipo                string       `bson:"tipo_veiculo" json:"type"`
	Status              string       `bson:"status" json:"status"`
	NumeroChassi        string       `bson:"numero_chassi,omitempty" json:"chassisNumber,omitempty"`
	Renavam             string       `bson:"renavam,omitempty" json:"renavam,omitempty"`
	Qrcode              string       `bson:"qrcode,omitempty" json:"qrCode,omitempty"`
	TipoCombustivel     string       `bson:"tipo_combustivel,omitempty" json:"fuelType,omitempty"`
	CapacidadeTanque    int          `bson:"capacidade_tanque,omitempty" json:"tankCapacity,omitempty"`
	Quilometragem       int          `bson:"quilometragem,omitempty" json:"mileage,omitempty"`
	CompanhiaSeguro     string       `bson:"companhia_seguro,omitempty" json:"insuranceCompany,omitempty"`
	ApoliceSeguro       string       `bson:"apolice_seguro,omitempty" json:"insurancePolicy,omitempty"`
	DataExpiracaoSeguro *time.Time   `bson:"data_expiracao_seguro,omitempty" json:"insuranceExpirationDate,omitempty"` // Usando ponteiro para permitir nulo
	Observacoes         string       `bson:"observacoes,omitempty" json:"observations,omitempty"`
	Foto                FotoMetadata `bson:"foto,omitempty" json:"photo,omitempty"`
	Documentos          []Anexo      `bson:"documentos,omitempty" json:"documents,omitempty"`
	ModelYear           string       `bson:"modelYear,omitempty" json:"modelYear,omitempty"`
	Notes               string       `bson:"notes,omitempty" json:"notes,omitempty"`
	Nickname            string       `bson:"nickname,omitempty" json:"nickname,omitempty"`
}

// MarshalJSON customizes the JSON representation of a Veiculo.
// It renames 'Foto' to 'FotoMetadata' and makes 'photo' a string URL.
func (v Veiculo) MarshalJSON() ([]byte, error) {
	type Alias Veiculo // Create an alias to avoid recursion with MarshalJSON.
	// The `json:"photo,omitempty"` tag on Alias.Foto will be ignored because we are explicitly setting `Photo` (string) and `FotoMetadata` (object).
	return json.Marshal(&struct {
		Photo string `json:"photo,omitempty"`
		*Alias
	}{
		Photo: v.Foto.URL,   // The 'photo' field will now be just the URL string.
		Alias: (*Alias)(&v), // Embed the original struct fields, except for the original Foto.
	})
}
