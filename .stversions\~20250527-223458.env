# Configurações do MongoDB
MONGO_URL=mongodb+srv://reginaldoroge:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0
MONGO_DB_NAME=WeDriver

# Configurações do servidor
SERVER_HOST=0.0.0.0
SERVER_PORT=8081
FRONTEND_URL=https://wedriver-admin.vercel.app

# Configurações de email (SMTP)
SMTP_HOST=srvalertas.weso.com.br
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=Flrtt@123#2025
SMTP_FROM=<EMAIL>

# Configurações de autenticação
JWT_SECRET=wedriver_secret_key_2025
# Configurações de autenticação
JWT_EXPIRATION_HOURS=24
REFRESH_TOKEN_EXPIRATION_DAYS=30

# .env.example
# ... outros exemplos de variáveis ...

# Configurações de Upload de Arquivos
UPLOADS_DIR=./uploads_data
# API_BASE_URL=http://localhost:8081
API_BASE_URL=https://wedrivergoapi.srv.weso.com.br
STATIC_ROUTE_PREFIX=/files