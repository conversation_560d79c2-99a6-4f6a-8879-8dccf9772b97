package handlers

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// IdentificacaoHandler gerencia as requisições relacionadas a identificações
type IdentificacaoHandler struct {
	client *mongo.Client
	dbName string
}

// NewIdentificacaoHandler cria uma nova instância de IdentificacaoHandler
func NewIdentificacaoHandler(client *mongo.Client, dbName string) *IdentificacaoHandler {
	return &IdentificacaoHandler{
		client: client,
		dbName: dbName,
	}
}

// GetIdentificacoes godoc
// @Summary Lista todas as identificações
// @Description Retorna uma lista de todas as identificações com opções de filtragem. Se nenhuma data for especificada, retorna apenas as identificações do dia atual.
// @Tags identificacoes
// @Accept json
// @Produce json
// @Param motorista query string false "Filtrar por ID do motorista (userMID)"
// @Param placa query string false "Filtrar por placa do veículo"
// @Param tipo query string false "Filtrar por tipo de identificação"
// @Param data_inicio query string false "Filtrar por data de criação (início) no formato YYYY-MM-DD. Se não for fornecido, usa a data atual."
// @Param data_fim query string false "Filtrar por data de criação (fim) no formato YYYY-MM-DD. Se não for fornecido, usa a data atual."
// @Param todos query bool false "Se true, retorna todas as identificações sem filtro de data. Padrão: false"
// @Param limit query int false "Limite de resultados por página (padrão: 50)"
// @Param skip query int false "Número de resultados para pular (para paginação)"
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /identificacao [get]
func (h *IdentificacaoHandler) GetIdentificacoes(c *gin.Context) {
	// Obter parâmetros de consulta
	motoristaID := c.Query("motorista")
	placa := c.Query("placa")
	tipo := c.Query("tipo")
	dataInicio := c.Query("data_inicio")
	dataFim := c.Query("data_fim")
	todosParam := c.Query("todos")

	// Verificar se deve retornar todos os registros sem filtro de data
	todos := false
	if todosParam == "true" || todosParam == "1" {
		todos = true
	}

	// Parâmetros de paginação
	limit := 50 // Valor padrão
	if limitParam := c.Query("limit"); limitParam != "" {
		if parsedLimit, err := parseInt(limitParam); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	skip := 0 // Valor padrão
	if skipParam := c.Query("skip"); skipParam != "" {
		if parsedSkip, err := parseInt(skipParam); err == nil && parsedSkip >= 0 {
			skip = parsedSkip
		}
	}

	// Construir o filtro
	filter := bson.M{}

	// Adicionar filtros se fornecidos
	if motoristaID != "" {
		filter["userMID"] = motoristaID
	}

	if placa != "" {
		filter["placa"] = placa
	}

	if tipo != "" {
		filter["type"] = tipo
	}

	// Filtro de data
	// Se não for especificado e 'todos' não for true, usa a data atual como padrão
	if dataInicio == "" && dataFim == "" && !todos {
		// Usar a data atual como filtro padrão
		now := time.Now()
		startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		endOfDay := startOfDay.Add(24 * time.Hour)

		filter["createdAt"] = bson.M{
			"$gte": startOfDay,
			"$lt":  endOfDay,
		}

		// Adicionar informação ao log
		log.Printf("Filtro de data não especificado, usando data atual: %s", startOfDay.Format("2006-01-02"))
	} else if dataInicio != "" || dataFim != "" {
		dateFilter := bson.M{}

		if dataInicio != "" {
			startDate, err := time.Parse("2006-01-02", dataInicio)
			if err == nil {
				dateFilter["$gte"] = startDate
			} else {
				log.Printf("Erro ao parsear data_inicio: %v", err)
			}
		}

		if dataFim != "" {
			endDate, err := time.Parse("2006-01-02", dataFim)
			if err == nil {
				// Adiciona um dia para incluir todo o dia final
				endDate = endDate.Add(24 * time.Hour)
				dateFilter["$lt"] = endDate
			} else {
				log.Printf("Erro ao parsear data_fim: %v", err)
			}
		}

		if len(dateFilter) > 0 {
			filter["createdAt"] = dateFilter
		}
	}

	// Obter a empresa do usuário autenticado
	empresaID, exists := c.Get("empresa_id")
	if exists && empresaID != nil {
		// Se o usuário pertence a uma empresa, filtrar apenas as identificações relacionadas a essa empresa
		// Isso requer que as identificações tenham um campo companyId ou similar
		// filter["companyId"] = empresaID
		// Nota: Descomente a linha acima se as identificações tiverem um campo de empresa
	}

	// Executar a consulta usando agregação para incluir o nome do motorista
	collection := h.client.Database(h.dbName).Collection("identificacoes")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Contar o total de documentos que correspondem ao filtro
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		log.Printf("Erro ao contar identificações: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao contar identificações"})
		return
	}

	// Criar pipeline de agregação
	pipeline := h.buildIdentificacaoAggregationPipeline(filter, skip, limit)

	// Executar agregação
	identificacoes, err := h.executeIdentificacaoAggregation(ctx, pipeline)
	if err != nil {
		log.Printf("Erro ao buscar identificações com agregação: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar identificações"})
		return
	}

	// Retornar resultados com metadados de paginação
	c.JSON(http.StatusOK, gin.H{
		"data": identificacoes,
		"meta": gin.H{
			"total":  total,
			"limit":  limit,
			"skip":   skip,
			"filter": filter,
		},
	})
}

// GetIdentificacaoByID godoc
// @Summary Obtém uma identificação pelo ID
// @Description Retorna os detalhes de uma identificação específica
// @Tags identificacoes
// @Accept json
// @Produce json
// @Param id path string true "ID da identificação"
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /identificacao/{id} [get]
func (h *IdentificacaoHandler) GetIdentificacaoByID(c *gin.Context) {
	id := c.Param("id")

	// Verificar se o ID é válido
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID não fornecido"})
		return
	}

	// Buscar a identificação no MongoDB usando agregação para incluir o nome do motorista
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Criar pipeline de agregação para buscar por ID específico
	filter := bson.M{"_id": id}
	pipeline := h.buildIdentificacaoAggregationPipeline(filter, 0, 1)

	// Executar agregação
	identificacoes, err := h.executeIdentificacaoAggregation(ctx, pipeline)
	if err != nil {
		log.Printf("Erro ao buscar identificação: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar identificação"})
		return
	}

	// Verificar se encontrou a identificação
	if len(identificacoes) == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Identificação não encontrada"})
		return
	}

	c.JSON(http.StatusOK, identificacoes[0])
}

// GetIdentificacoesByMotorista godoc
// @Summary Lista identificações por motorista
// @Description Retorna uma lista de identificações filtradas por ID do motorista
// @Tags identificacoes
// @Accept json
// @Produce json
// @Param motorista_id path string true "ID do motorista"
// @Param limit query int false "Limite de resultados por página (padrão: 50)"
// @Param skip query int false "Número de resultados para pular (para paginação)"
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /identificacao/motorista/{motorista_id} [get]
func (h *IdentificacaoHandler) GetIdentificacoesByMotorista(c *gin.Context) {
	motoristaID := c.Param("motorista_id")

	// Verificar se o ID do motorista é válido
	if motoristaID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do motorista não fornecido"})
		return
	}

	// Parâmetros de paginação
	limit := 50 // Valor padrão
	if limitParam := c.Query("limit"); limitParam != "" {
		if parsedLimit, err := parseInt(limitParam); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	skip := 0 // Valor padrão
	if skipParam := c.Query("skip"); skipParam != "" {
		if parsedSkip, err := parseInt(skipParam); err == nil && parsedSkip >= 0 {
			skip = parsedSkip
		}
	}

	// Construir o filtro
	filter := bson.M{"userMID": motoristaID}

	// Executar a consulta usando agregação para incluir o nome do motorista
	collection := h.client.Database(h.dbName).Collection("identificacoes")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Contar o total de documentos que correspondem ao filtro
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		log.Printf("Erro ao contar identificações: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao contar identificações"})
		return
	}

	// Criar pipeline de agregação
	pipeline := h.buildIdentificacaoAggregationPipeline(filter, skip, limit)

	// Executar agregação
	identificacoes, err := h.executeIdentificacaoAggregation(ctx, pipeline)
	if err != nil {
		log.Printf("Erro ao buscar identificações com agregação: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar identificações"})
		return
	}

	// Retornar resultados com metadados de paginação
	c.JSON(http.StatusOK, gin.H{
		"data": identificacoes,
		"meta": gin.H{
			"total":  total,
			"limit":  limit,
			"skip":   skip,
			"filter": filter,
		},
	})
}

// GetIdentificacoesByVeiculo godoc
// @Summary Lista identificações por veículo
// @Description Retorna uma lista de identificações filtradas por placa do veículo
// @Tags identificacoes
// @Accept json
// @Produce json
// @Param placa path string true "Placa do veículo"
// @Param limit query int false "Limite de resultados por página (padrão: 50)"
// @Param skip query int false "Número de resultados para pular (para paginação)"
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /identificacao/veiculo/{placa} [get]
func (h *IdentificacaoHandler) GetIdentificacoesByVeiculo(c *gin.Context) {
	placa := c.Param("placa")

	// Verificar se a placa é válida
	if placa == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Placa do veículo não fornecida"})
		return
	}

	// Parâmetros de paginação
	limit := 50 // Valor padrão
	if limitParam := c.Query("limit"); limitParam != "" {
		if parsedLimit, err := parseInt(limitParam); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	skip := 0 // Valor padrão
	if skipParam := c.Query("skip"); skipParam != "" {
		if parsedSkip, err := parseInt(skipParam); err == nil && parsedSkip >= 0 {
			skip = parsedSkip
		}
	}

	// Construir o filtro
	filter := bson.M{"placa": placa}

	// Executar a consulta usando agregação para incluir o nome do motorista
	collection := h.client.Database(h.dbName).Collection("identificacoes")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Contar o total de documentos que correspondem ao filtro
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		log.Printf("Erro ao contar identificações: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao contar identificações"})
		return
	}

	// Criar pipeline de agregação
	pipeline := h.buildIdentificacaoAggregationPipeline(filter, skip, limit)

	// Executar agregação
	identificacoes, err := h.executeIdentificacaoAggregation(ctx, pipeline)
	if err != nil {
		log.Printf("Erro ao buscar identificações com agregação: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar identificações"})
		return
	}

	// Retornar resultados com metadados de paginação
	c.JSON(http.StatusOK, gin.H{
		"data": identificacoes,
		"meta": gin.H{
			"total":  total,
			"limit":  limit,
			"skip":   skip,
			"filter": filter,
		},
	})
}

// buildIdentificacaoAggregationPipeline cria o pipeline de agregação para incluir o nome do motorista
func (h *IdentificacaoHandler) buildIdentificacaoAggregationPipeline(filter bson.M, skip, limit int) []bson.M {
	pipeline := []bson.M{
		// 1. Filtrar identificações
		{"$match": filter},

		// 2. Converter userMID string para ObjectID para o lookup (com validação mais robusta)
		{"$addFields": bson.M{
			"userMIDObjectId": bson.M{
				"$cond": bson.M{
					"if": bson.M{
						"$and": []bson.M{
							{"$ne": []interface{}{"$userMID", ""}},
							{"$ne": []interface{}{"$userMID", nil}},
							{"$eq": []interface{}{bson.M{"$type": "$userMID"}, "string"}},
							{"$eq": []interface{}{bson.M{"$strLenCP": "$userMID"}, 24}}, // Verificar se tem 24 caracteres (ObjectID válido)
						},
					},
					"then": bson.M{
						"$cond": bson.M{
							"if": bson.M{"$regexMatch": bson.M{
								"input": "$userMID",
								"regex": "^[0-9a-fA-F]{24}$", // Verificar se é hexadecimal válido
							}},
							"then": bson.M{"$toObjectId": "$userMID"},
							"else": nil,
						},
					},
					"else": nil,
				},
			},
		}},

		// 3. JOIN com a coleção motoristas
		{"$lookup": bson.M{
			"from":         "motoristas",
			"localField":   "userMIDObjectId",
			"foreignField": "_id",
			"as":           "motorista_info",
		}},

		// 4. Adicionar campo 'name' com o nome do motorista (com fallback para motorista_log se disponível)
		{"$addFields": bson.M{
			"name": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$gt": []interface{}{bson.M{"$size": "$motorista_info"}, 0}},
					"then": bson.M{"$arrayElemAt": []interface{}{"$motorista_info.nome", 0}},
					"else": bson.M{
						"$cond": bson.M{
							"if":   bson.M{"$ne": []interface{}{"$motorista_log.nome", nil}},
							"then": "$motorista_log.nome",
							"else": "Motorista não encontrado",
						},
					},
				},
			},
		}},

		// 5. Remover campos temporários
		{"$project": bson.M{
			"motorista_info": 0, // Remove dados temporários do lookup
		}},

		// 6. Ordenação
		{"$sort": bson.M{"createdAt": -1}},

		// 7. Paginação
		{"$skip": skip},
		{"$limit": limit},
	}

	return pipeline
}

// executeIdentificacaoAggregation executa a agregação e retorna os resultados
func (h *IdentificacaoHandler) executeIdentificacaoAggregation(ctx context.Context, pipeline []bson.M) ([]bson.M, error) {
	collection := h.client.Database(h.dbName).Collection("identificacoes")

	// Log do pipeline para debug
	log.Printf("Executando pipeline de agregação com %d estágios", len(pipeline))

	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("erro ao executar agregação: %w", err)
	}
	defer cursor.Close(ctx)

	var identificacoes []bson.M
	if err := cursor.All(ctx, &identificacoes); err != nil {
		return nil, fmt.Errorf("erro ao decodificar resultados da agregação: %w", err)
	}

	// Log para debug - verificar se os nomes dos motoristas estão sendo encontrados
	log.Printf("Agregação executada com sucesso. %d documentos retornados", len(identificacoes))

	// Log para debug - verificar alguns exemplos para debug
	for i, doc := range identificacoes {
		if i >= 3 { // Mostrar apenas os primeiros 3 para não poluir o log
			break
		}
		userMID := "N/A"
		name := "N/A"
		
		if val, exists := doc["userMID"]; exists && val != nil {
			userMID = fmt.Sprintf("%v", val)
		}
		if val, exists := doc["name"]; exists && val != nil {
			name = fmt.Sprintf("%v", val)
		}
		
		log.Printf("Exemplo %d - userMID: %s, name: %s", i+1, userMID, name)
	}

	return identificacoes, nil
}

// Função auxiliar para converter string para int
func parseInt(s string) (int, error) {
	var i int
	_, err := fmt.Sscanf(s, "%d", &i)
	return i, err
}

// DebugIdentificacaoMotorista godoc
// @Summary Debug da busca de motorista em identificações
// @Description Endpoint de debug para verificar problemas na busca de motoristas nas identificações
// @Tags identificacoes
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /identificacao/debug [get]
func (h *IdentificacaoHandler) DebugIdentificacaoMotorista(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	collection := h.client.Database(h.dbName).Collection("identificacoes")

	// 1. Verificar estatísticas básicas
	stats := make(map[string]interface{})

	// Total de identificações
	total, err := collection.CountDocuments(ctx, bson.M{})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao contar identificações"})
		return
	}
	stats["total_identificacoes"] = total

	// Identificações com userMID não vazio
	comUserMID, err := collection.CountDocuments(ctx, bson.M{
		"userMID": bson.M{"$ne": ""},
	})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao contar identificações com userMID"})
		return
	}
	stats["com_userMID"] = comUserMID

	// Identificações sem userMID
	semUserMID, err := collection.CountDocuments(ctx, bson.M{
		"$or": []bson.M{
			{"userMID": ""},
			{"userMID": nil},
			{"userMID": bson.M{"$exists": false}},
		},
	})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao contar identificações sem userMID"})
		return
	}
	stats["sem_userMID"] = semUserMID

	// 2. Verificar exemplos de userMID
	cursor, err := collection.Find(ctx, bson.M{
		"userMID": bson.M{"$ne": ""},
	}, options.Find().SetLimit(5))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar exemplos"})
		return
	}
	defer cursor.Close(ctx)

	var exemplos []map[string]interface{}
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			continue
		}
		exemplo := map[string]interface{}{
			"_id":          doc["_id"],
			"userMID":      doc["userMID"],
			"userMID_type": fmt.Sprintf("%T", doc["userMID"]),
		}

		// Verificar se é um ObjectID válido
		if userMIDStr, ok := doc["userMID"].(string); ok {
			if len(userMIDStr) == 24 {
				if _, err := primitive.ObjectIDFromHex(userMIDStr); err == nil {
					exemplo["userMID_valid"] = true
				} else {
					exemplo["userMID_valid"] = false
					exemplo["userMID_error"] = err.Error()
				}
			} else {
				exemplo["userMID_valid"] = false
				exemplo["userMID_error"] = "Comprimento inválido"
			}
		}

		exemplos = append(exemplos, exemplo)
	}
	stats["exemplos_userMID"] = exemplos

	// 3. Verificar motoristas na coleção
	motoristasCollection := h.client.Database(h.dbName).Collection("motoristas")
	totalMotoristas, err := motoristasCollection.CountDocuments(ctx, bson.M{})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao contar motoristas"})
		return
	}
	stats["total_motoristas"] = totalMotoristas

	// 4. Testar pipeline de agregação com um exemplo
	if len(exemplos) > 0 {
		if userMID, exists := exemplos[0]["userMID"]; exists {
			filter := bson.M{"userMID": userMID}
			pipeline := h.buildIdentificacaoAggregationPipeline(filter, 0, 1)

			testResult, err := h.executeIdentificacaoAggregation(ctx, pipeline)
			if err != nil {
				stats["teste_pipeline_erro"] = err.Error()
			} else {
				stats["teste_pipeline_resultado"] = testResult
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"debug_info": stats,
	})
}

// DebugIdentificacaoRaw godoc
// @Summary Debug dos dados brutos das identificações
// @Description Endpoint para verificar os dados brutos das identificações sem agregação
// @Tags identificacoes
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /identificacao/debug-raw [get]
func (h *IdentificacaoHandler) DebugIdentificacaoRaw(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	collection := h.client.Database(h.dbName).Collection("identificacoes")
	
	// Buscar alguns documentos brutos sem agregação
	cursor, err := collection.Find(ctx, bson.M{}, options.Find().SetLimit(5))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar identificações"})
		return
	}
	defer cursor.Close(ctx)
	
	var documentos []bson.M
	if err := cursor.All(ctx, &documentos); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao decodificar documentos"})
		return
	}
	
	// Analisar os documentos
	stats := make(map[string]interface{})
	stats["total_documentos"] = len(documentos)
	
	var exemplos []map[string]interface{}
	for i, doc := range documentos {
		exemplo := map[string]interface{}{
			"documento_index": i + 1,
			"_id": doc["_id"],
		}
		
		// Verificar se tem userMID
		if userMID, exists := doc["userMID"]; exists {
			exemplo["userMID"] = userMID
			exemplo["userMID_type"] = fmt.Sprintf("%T", userMID)
			exemplo["userMID_exists"] = true
			
			// Se for string, verificar se é ObjectID válido
			if userMIDStr, ok := userMID.(string); ok {
				exemplo["userMID_length"] = len(userMIDStr)
				if len(userMIDStr) == 24 {
					if _, err := primitive.ObjectIDFromHex(userMIDStr); err == nil {
						exemplo["userMID_valid_objectid"] = true
					} else {
						exemplo["userMID_valid_objectid"] = false
						exemplo["userMID_error"] = err.Error()
					}
				} else {
					exemplo["userMID_valid_objectid"] = false
					exemplo["userMID_error"] = "Comprimento inválido"
				}
			}
		} else {
			exemplo["userMID_exists"] = false
		}
		
		// Verificar se tem motorista_log
		if motoristaLog, exists := doc["motorista_log"]; exists {
			exemplo["motorista_log_exists"] = true
			if logMap, ok := motoristaLog.(map[string]interface{}); ok {
				if nome, nomeExists := logMap["nome"]; nomeExists {
					exemplo["motorista_log_nome"] = nome
				}
			}
		} else {
			exemplo["motorista_log_exists"] = false
		}
		
		// Listar todos os campos do primeiro documento
		if i == 0 {
			var campos []string
			for key := range doc {
				campos = append(campos, key)
			}
			exemplo["todos_os_campos"] = campos
		}
		
		exemplos = append(exemplos, exemplo)
	}
	
	stats["exemplos"] = exemplos
	
	c.JSON(http.StatusOK, gin.H{
		"debug_raw": stats,
	})
}
