package handlers

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// IdentificacaoHandler gerencia as requisições relacionadas a identificações
type IdentificacaoHandler struct {
	client *mongo.Client
	dbName string
}

// NewIdentificacaoHandler cria uma nova instância de IdentificacaoHandler
func NewIdentificacaoHandler(client *mongo.Client, dbName string) *IdentificacaoHandler {
	return &IdentificacaoHandler{
		client: client,
		dbName: dbName,
	}
}

// GetIdentificacoes godoc
// @Summary Lista todas as identificações
// @Description Retorna uma lista de todas as identificações com opções de filtragem. Se nenhuma data for especificada, retorna apenas as identificações do dia atual.
// @Tags identificacoes
// @Accept json
// @Produce json
// @Param motorista query string false "Filtrar por ID do motorista (userMID)"
// @Param placa query string false "Filtrar por placa do veículo"
// @Param tipo query string false "Filtrar por tipo de identificação"
// @Param data_inicio query string false "Filtrar por data de criação (início) no formato YYYY-MM-DD. Se não for fornecido, usa a data atual."
// @Param data_fim query string false "Filtrar por data de criação (fim) no formato YYYY-MM-DD. Se não for fornecido, usa a data atual."
// @Param todos query bool false "Se true, retorna todas as identificações sem filtro de data. Padrão: false"
// @Param limit query int false "Limite de resultados por página (padrão: 50)"
// @Param skip query int false "Número de resultados para pular (para paginação)"
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /identificacao [get]
func (h *IdentificacaoHandler) GetIdentificacoes(c *gin.Context) {
	// Obter parâmetros de consulta
	motoristaID := c.Query("motorista")
	placa := c.Query("placa")
	tipo := c.Query("tipo")
	dataInicio := c.Query("data_inicio")
	dataFim := c.Query("data_fim")
	todosParam := c.Query("todos")

	// Verificar se deve retornar todos os registros sem filtro de data
	todos := false
	if todosParam == "true" || todosParam == "1" {
		todos = true
	}

	// Parâmetros de paginação
	limit := 50 // Valor padrão
	if limitParam := c.Query("limit"); limitParam != "" {
		if parsedLimit, err := parseInt(limitParam); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	skip := 0 // Valor padrão
	if skipParam := c.Query("skip"); skipParam != "" {
		if parsedSkip, err := parseInt(skipParam); err == nil && parsedSkip >= 0 {
			skip = parsedSkip
		}
	}

	// Construir o filtro
	filter := bson.M{}

	// Adicionar filtros se fornecidos
	if motoristaID != "" {
		filter["userMID"] = motoristaID
	}

	if placa != "" {
		filter["placa"] = placa
	}

	if tipo != "" {
		filter["type"] = tipo
	}

	// Filtro de data
	// Se não for especificado e 'todos' não for true, usa a data atual como padrão
	if dataInicio == "" && dataFim == "" && !todos {
		// Usar a data atual como filtro padrão
		now := time.Now()
		startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		endOfDay := startOfDay.Add(24 * time.Hour)

		filter["createdAt"] = bson.M{
			"$gte": startOfDay,
			"$lt":  endOfDay,
		}

		// Adicionar informação ao log
		log.Printf("Filtro de data não especificado, usando data atual: %s", startOfDay.Format("2006-01-02"))
	} else if dataInicio != "" || dataFim != "" {
		dateFilter := bson.M{}

		if dataInicio != "" {
			startDate, err := time.Parse("2006-01-02", dataInicio)
			if err == nil {
				dateFilter["$gte"] = startDate
			} else {
				log.Printf("Erro ao parsear data_inicio: %v", err)
			}
		}

		if dataFim != "" {
			endDate, err := time.Parse("2006-01-02", dataFim)
			if err == nil {
				// Adiciona um dia para incluir todo o dia final
				endDate = endDate.Add(24 * time.Hour)
				dateFilter["$lt"] = endDate
			} else {
				log.Printf("Erro ao parsear data_fim: %v", err)
			}
		}

		if len(dateFilter) > 0 {
			filter["createdAt"] = dateFilter
		}
	}

	// Obter a empresa do usuário autenticado
	empresaID, exists := c.Get("empresa_id")
	if exists && empresaID != nil {
		// Se o usuário pertence a uma empresa, filtrar apenas as identificações relacionadas a essa empresa
		// Isso requer que as identificações tenham um campo companyId ou similar
		// filter["companyId"] = empresaID
		// Nota: Descomente a linha acima se as identificações tiverem um campo de empresa
	}

	// Executar a consulta usando agregação para incluir o nome do motorista
	collection := h.client.Database(h.dbName).Collection("identificacoes")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Contar o total de documentos que correspondem ao filtro
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		log.Printf("Erro ao contar identificações: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao contar identificações"})
		return
	}

	// Criar pipeline de agregação
	pipeline := h.buildIdentificacaoAggregationPipeline(filter, skip, limit)

	// Executar agregação
	identificacoes, err := h.executeIdentificacaoAggregation(ctx, pipeline)
	if err != nil {
		log.Printf("Erro ao buscar identificações com agregação: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar identificações"})
		return
	}

	// Retornar resultados com metadados de paginação
	c.JSON(http.StatusOK, gin.H{
		"data": identificacoes,
		"meta": gin.H{
			"total":  total,
			"limit":  limit,
			"skip":   skip,
			"filter": filter,
		},
	})
}

// GetIdentificacaoByID godoc
// @Summary Obtém uma identificação pelo ID
// @Description Retorna os detalhes de uma identificação específica
// @Tags identificacoes
// @Accept json
// @Produce json
// @Param id path string true "ID da identificação"
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /identificacao/{id} [get]
func (h *IdentificacaoHandler) GetIdentificacaoByID(c *gin.Context) {
	id := c.Param("id")

	// Verificar se o ID é válido
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID não fornecido"})
		return
	}

	// Buscar a identificação no MongoDB usando agregação para incluir o nome do motorista
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Criar pipeline de agregação para buscar por ID específico
	filter := bson.M{"_id": id}
	pipeline := h.buildIdentificacaoAggregationPipeline(filter, 0, 1)

	// Executar agregação
	identificacoes, err := h.executeIdentificacaoAggregation(ctx, pipeline)
	if err != nil {
		log.Printf("Erro ao buscar identificação: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar identificação"})
		return
	}

	// Verificar se encontrou a identificação
	if len(identificacoes) == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Identificação não encontrada"})
		return
	}

	c.JSON(http.StatusOK, identificacoes[0])
}

// GetIdentificacoesByMotorista godoc
// @Summary Lista identificações por motorista
// @Description Retorna uma lista de identificações filtradas por ID do motorista
// @Tags identificacoes
// @Accept json
// @Produce json
// @Param motorista_id path string true "ID do motorista"
// @Param limit query int false "Limite de resultados por página (padrão: 50)"
// @Param skip query int false "Número de resultados para pular (para paginação)"
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /identificacao/motorista/{motorista_id} [get]
func (h *IdentificacaoHandler) GetIdentificacoesByMotorista(c *gin.Context) {
	motoristaID := c.Param("motorista_id")

	// Verificar se o ID do motorista é válido
	if motoristaID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do motorista não fornecido"})
		return
	}

	// Parâmetros de paginação
	limit := 50 // Valor padrão
	if limitParam := c.Query("limit"); limitParam != "" {
		if parsedLimit, err := parseInt(limitParam); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	skip := 0 // Valor padrão
	if skipParam := c.Query("skip"); skipParam != "" {
		if parsedSkip, err := parseInt(skipParam); err == nil && parsedSkip >= 0 {
			skip = parsedSkip
		}
	}

	// Construir o filtro
	filter := bson.M{"userMID": motoristaID}

	// Executar a consulta usando agregação para incluir o nome do motorista
	collection := h.client.Database(h.dbName).Collection("identificacoes")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Contar o total de documentos que correspondem ao filtro
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		log.Printf("Erro ao contar identificações: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao contar identificações"})
		return
	}

	// Criar pipeline de agregação
	pipeline := h.buildIdentificacaoAggregationPipeline(filter, skip, limit)

	// Executar agregação
	identificacoes, err := h.executeIdentificacaoAggregation(ctx, pipeline)
	if err != nil {
		log.Printf("Erro ao buscar identificações com agregação: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar identificações"})
		return
	}

	// Retornar resultados com metadados de paginação
	c.JSON(http.StatusOK, gin.H{
		"data": identificacoes,
		"meta": gin.H{
			"total":  total,
			"limit":  limit,
			"skip":   skip,
			"filter": filter,
		},
	})
}

// GetIdentificacoesByVeiculo godoc
// @Summary Lista identificações por veículo
// @Description Retorna uma lista de identificações filtradas por placa do veículo
// @Tags identificacoes
// @Accept json
// @Produce json
// @Param placa path string true "Placa do veículo"
// @Param limit query int false "Limite de resultados por página (padrão: 50)"
// @Param skip query int false "Número de resultados para pular (para paginação)"
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /identificacao/veiculo/{placa} [get]
func (h *IdentificacaoHandler) GetIdentificacoesByVeiculo(c *gin.Context) {
	placa := c.Param("placa")

	// Verificar se a placa é válida
	if placa == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Placa do veículo não fornecida"})
		return
	}

	// Parâmetros de paginação
	limit := 50 // Valor padrão
	if limitParam := c.Query("limit"); limitParam != "" {
		if parsedLimit, err := parseInt(limitParam); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	skip := 0 // Valor padrão
	if skipParam := c.Query("skip"); skipParam != "" {
		if parsedSkip, err := parseInt(skipParam); err == nil && parsedSkip >= 0 {
			skip = parsedSkip
		}
	}

	// Construir o filtro
	filter := bson.M{"placa": placa}

	// Executar a consulta usando agregação para incluir o nome do motorista
	collection := h.client.Database(h.dbName).Collection("identificacoes")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Contar o total de documentos que correspondem ao filtro
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		log.Printf("Erro ao contar identificações: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao contar identificações"})
		return
	}

	// Criar pipeline de agregação
	pipeline := h.buildIdentificacaoAggregationPipeline(filter, skip, limit)

	// Executar agregação
	identificacoes, err := h.executeIdentificacaoAggregation(ctx, pipeline)
	if err != nil {
		log.Printf("Erro ao buscar identificações com agregação: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar identificações"})
		return
	}

	// Retornar resultados com metadados de paginação
	c.JSON(http.StatusOK, gin.H{
		"data": identificacoes,
		"meta": gin.H{
			"total":  total,
			"limit":  limit,
			"skip":   skip,
			"filter": filter,
		},
	})
}

// buildIdentificacaoAggregationPipeline cria o pipeline de agregação para incluir o nome do motorista e dados do veículo
func (h *IdentificacaoHandler) buildIdentificacaoAggregationPipeline(filter bson.M, skip, limit int) []bson.M {
	pipeline := []bson.M{
		// 1. Filtrar identificações
		{"$match": filter},

		// 2. Converter userMID string para ObjectID para o lookup (com validação mais robusta)
		{"$addFields": bson.M{
			"userMIDObjectId": bson.M{
				"$cond": bson.M{
					"if": bson.M{
						"$and": []bson.M{
							{"$ne": []interface{}{"$userMID", ""}},
							{"$ne": []interface{}{"$userMID", nil}},
							{"$eq": []interface{}{bson.M{"$type": "$userMID"}, "string"}},
							{"$eq": []interface{}{bson.M{"$strLenCP": "$userMID"}, 24}}, // Verificar se tem 24 caracteres (ObjectID válido)
						},
					},
					"then": bson.M{
						"$cond": bson.M{
							"if": bson.M{"$regexMatch": bson.M{
								"input": "$userMID",
								"regex": "^[0-9a-fA-F]{24}$", // Verificar se é hexadecimal válido
							}},
							"then": bson.M{"$toObjectId": "$userMID"},
							"else": nil,
						},
					},
					"else": nil,
				},
			},
		}},

		// 3. Converter vehicleId string para ObjectID para o lookup (seguindo o mesmo padrão do userMID)
		{"$addFields": bson.M{
			"vehicleIdObjectId": bson.M{
				"$cond": bson.M{
					"if": bson.M{
						"$and": []bson.M{
							{"$ne": []interface{}{"$vehicleId", ""}},
							{"$ne": []interface{}{"$vehicleId", nil}},
							{"$eq": []interface{}{bson.M{"$type": "$vehicleId"}, "string"}},
							{"$eq": []interface{}{bson.M{"$strLenCP": "$vehicleId"}, 24}}, // Verificar se tem 24 caracteres (ObjectID válido)
						},
					},
					"then": bson.M{
						"$cond": bson.M{
							"if": bson.M{"$regexMatch": bson.M{
								"input": "$vehicleId",
								"regex": "^[0-9a-fA-F]{24}$", // Verificar se é hexadecimal válido
							}},
							"then": bson.M{"$toObjectId": "$vehicleId"},
							"else": nil,
						},
					},
					"else": nil,
				},
			},
		}},

		// 4. JOIN com a coleção motoristas
		{"$lookup": bson.M{
			"from":         "motoristas",
			"localField":   "userMIDObjectId",
			"foreignField": "_id",
			"as":           "motorista_info",
		}},

		// 5. JOIN com a coleção veiculos
		{"$lookup": bson.M{
			"from":         "veiculos",
			"localField":   "vehicleIdObjectId",
			"foreignField": "_id",
			"as":           "veiculo_info",
		}},

		// 6. Adicionar campo 'name' com o nome do motorista (com fallback para motorista_log se disponível)
		{"$addFields": bson.M{
			"name": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$gt": []interface{}{bson.M{"$size": "$motorista_info"}, 0}},
					"then": bson.M{"$arrayElemAt": []interface{}{"$motorista_info.nome", 0}},
					"else": bson.M{
						"$cond": bson.M{
							"if":   bson.M{"$ne": []interface{}{"$motorista_log.nome", nil}},
							"then": "$motorista_log.nome",
							"else": "Motorista não encontrado",
						},
					},
				},
			},
		}},

		// 7. Adicionar campos do veículo
		{"$addFields": bson.M{
			"vehicle_id": "$vehicleId", // Incluir o vehicleId original na resposta
			"vehicle_plate": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$gt": []interface{}{bson.M{"$size": "$veiculo_info"}, 0}},
					"then": bson.M{"$arrayElemAt": []interface{}{"$veiculo_info.placa", 0}},
					"else": nil,
				},
			},
			"vehicle_model": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$gt": []interface{}{bson.M{"$size": "$veiculo_info"}, 0}},
					"then": bson.M{"$arrayElemAt": []interface{}{"$veiculo_info.modelo", 0}},
					"else": nil,
				},
			},
			"vehicle_brand": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$gt": []interface{}{bson.M{"$size": "$veiculo_info"}, 0}},
					"then": bson.M{"$arrayElemAt": []interface{}{"$veiculo_info.marca", 0}},
					"else": nil,
				},
			},
			"vehicle_nickname": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$gt": []interface{}{bson.M{"$size": "$veiculo_info"}, 0}},
					"then": bson.M{"$arrayElemAt": []interface{}{"$veiculo_info.nickname", 0}},
					"else": nil,
				},
			},
		}},

		// 8. Remover campos temporários
		{"$project": bson.M{
			"motorista_info":    0, // Remove dados temporários do lookup
			"veiculo_info":      0, // Remove dados temporários do lookup
			"userMIDObjectId":   0, // Remove campo temporário de conversão
			"vehicleIdObjectId": 0, // Remove campo temporário de conversão
		}},

		// 9. Ordenação
		{"$sort": bson.M{"createdAt": -1}},

		// 10. Paginação
		{"$skip": skip},
		{"$limit": limit},
	}

	return pipeline
}

// executeIdentificacaoAggregation executa a agregação e retorna os resultados
func (h *IdentificacaoHandler) executeIdentificacaoAggregation(ctx context.Context, pipeline []bson.M) ([]bson.M, error) {
	collection := h.client.Database(h.dbName).Collection("identificacoes")

	// Log do pipeline para debug
	log.Printf("Executando pipeline de agregação com %d estágios", len(pipeline))

	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("erro ao executar agregação: %w", err)
	}
	defer cursor.Close(ctx)

	var identificacoes []bson.M
	if err := cursor.All(ctx, &identificacoes); err != nil {
		return nil, fmt.Errorf("erro ao decodificar resultados da agregação: %w", err)
	}

	// Log para debug - verificar se os nomes dos motoristas estão sendo encontrados
	log.Printf("Agregação executada com sucesso. %d documentos retornados", len(identificacoes))

	// Log para debug - verificar alguns exemplos para debug
	for i, doc := range identificacoes {
		if i >= 3 { // Mostrar apenas os primeiros 3 para não poluir o log
			break
		}
		userMID := "N/A"
		name := "N/A"
		vehicleId := "N/A"
		vehiclePlate := "N/A"
		vehicleNickname := "N/A"

		if val, exists := doc["userMID"]; exists && val != nil {
			userMID = fmt.Sprintf("%v", val)
		}
		if val, exists := doc["name"]; exists && val != nil {
			name = fmt.Sprintf("%v", val)
		}
		if val, exists := doc["vehicle_id"]; exists && val != nil {
			vehicleId = fmt.Sprintf("%v", val)
		}
		if val, exists := doc["vehicle_plate"]; exists && val != nil {
			vehiclePlate = fmt.Sprintf("%v", val)
		}
		if val, exists := doc["vehicle_nickname"]; exists && val != nil {
			vehicleNickname = fmt.Sprintf("%v", val)
		}

		log.Printf("Exemplo %d - userMID: %s, name: %s, vehicleId: %s, vehicle_plate: %s, vehicle_nickname: %s", i+1, userMID, name, vehicleId, vehiclePlate, vehicleNickname)
	}

	return identificacoes, nil
}

// Função auxiliar para converter string para int
func parseInt(s string) (int, error) {
	var i int
	_, err := fmt.Sscanf(s, "%d", &i)
	return i, err
}
