package models

import (
	"encoding/json"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Endereco já deve estar definido em models/empresa.go ou similar
// Removendo a definição duplicada daqui.

// Motorista representa um motorista no sistema
type Motorista struct {
	// Campos de configuração e permissões Firestore
	Config         map[string]bool `bson:"config" json:"config,omitempty"`
	Permissions    []string        `bson:"permissions" json:"permissions,omitempty"`
	Timezone       string          `bson:"timezone" json:"timezone,omitempty"`
	TimezoneOffset int             `bson:"timezone_offset" json:"timezoneOffset,omitempty"`

	ID              primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`
	Empresa         primitive.ObjectID `bson:"empresa" json:"empresa"` // J<PERSON> existe, será preenchido pelo token
	CreatedAt       time.Time          `bson:"created_at" json:"created_at"`
	UpdatedAt       time.Time          `bson:"updated_at" json:"updated_at"`
	Ativo           bool               `bson:"ativo" json:"active"` // Mapeado de 'active' no JSON
	Modulos         map[string]bool    `bson:"modulos" json:"modules,omitempty"`
	Anexos          []Anexo            `bson:"anexos" json:"attachments,omitempty"`
	FirestoreUserID string             `bson:"firestore_user_id" json:"firestore_user_id,omitempty"` // ID do documento do usuário no Firestore

	// Novos campos do Firestore para sincronização
	ActivatedAt     *time.Time `bson:"activated_at" json:"activated_at,omitempty"`
	FirebaseEmail   *string    `bson:"firebase_email" json:"firebase_email,omitempty"`
	FirebaseAuthUID *string    `bson:"firebase_auth_uid" json:"firebase_auth_uid,omitempty"`

	// Campos específicos do motorista do JSON
	Nome               string       `bson:"nome" json:"name"` // 'name' no JSON
	CPF                string       `bson:"cpf" json:"cpf"`
	RG                 string       `bson:"rg" json:"rg,omitempty"`
	DataNasc           *time.Time   `bson:"data_nasc" json:"birthDate,omitempty"` // 'birthDate' no JSON
	Genero             string       `bson:"genero" json:"gender,omitempty"`
	Telefone           string       `bson:"telefone" json:"phone"` // 'phone' no JSON
	Email              string       `bson:"email" json:"email"`
	ContatoEmergencia  string       `bson:"contato_emergencia" json:"emergencyContact,omitempty"`
	TelefoneEmergencia string       `bson:"telefone_emergencia" json:"emergencyPhone,omitempty"`
	TipoSanguineo      string       `bson:"tipo_sanguineo" json:"bloodType,omitempty"`
	Observacoes        string       `bson:"observacoes" json:"observations,omitempty"`
	CodigoVerificacao  string       `bson:"codigo_verificacao" json:"verificationCode,omitempty"`
	AppStatus          string       `bson:"app_status" json:"appStatus,omitempty"` // e.g., "pending"
	Foto               FotoMetadata `bson:"foto,omitempty" json:"photo,omitempty"`
	TipoUsuario        string       `bson:"tipo_usuario" json:"typeUser,omitempty"`

	// Campos de CNH (achatados)
	CNHNumero        string     `bson:"cnh_numero" json:"cnh,omitempty"`
	CNHCategoria     string     `bson:"cnh_categoria" json:"cnhCategory,omitempty"`
	CNHValidade      *time.Time `bson:"cnh_validade" json:"cnhExpiration,omitempty"`
	CNHEstadoEmissao string     `bson:"cnh_estado_emissao" json:"cnhState,omitempty"`
	CNHDataEmissao   *time.Time `bson:"cnh_data_emissao" json:"cnhIssuanceDate,omitempty"`

	// Campos de Endereco (achatados)
	CEP            string `bson:"cep" json:"cep,omitempty"`
	Logradouro     string `bson:"logradouro" json:"street,omitempty"`
	NumeroEndereco string `bson:"numero_endereco" json:"number,omitempty"`
	Complemento    string `bson:"complemento" json:"complement,omitempty"`
	Bairro         string `bson:"bairro" json:"neighborhood,omitempty"`
	Cidade         string `bson:"cidade" json:"city,omitempty"`
	Estado         string `bson:"estado" json:"state,omitempty"`
}

// MarshalJSON customizes the JSON representation of a Motorista.
// It renames 'Foto' to 'FotoMetadata' and makes 'photo' a string URL.
func (m Motorista) MarshalJSON() ([]byte, error) {
	type Alias Motorista // Create an alias to avoid recursion with MarshalJSON.
	// The `json:"photo,omitempty"` tag on Alias.Foto will be ignored because we are explicitly setting `Photo` (string) and `FotoMetadata` (object).
	return json.Marshal(&struct {
		Photo string `json:"photo,omitempty"`
		*Alias
	}{
		Photo: m.Foto.URL,   // The 'photo' field will now be just the URL string.
		Alias: (*Alias)(&m), // Embed the original struct fields, except for the original Foto.
	})
}

// Anexo representa um documento ou arquivo anexado ao motorista
type Anexo struct {
	ID         primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`
	Nome       string             `bson:"name" json:"name"`
	Tipo       string             `bson:"type" json:"type"`
	URL        string             `bson:"url" json:"url"`
	DataUpload *time.Time         `bson:"data_upload" json:"data_upload"`
	Descricao  string             `bson:"description,omitempty" json:"description,omitempty"`
}
