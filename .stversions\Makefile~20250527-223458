# Variáveis
BINARY_NAME=wedriverapigo
BUILD_DIR=build
DOCKER_IMAGE=wedriverapigo
GO_FILES=$(shell find . -name '*.go' -not -path "./vendor/*")

# Detecta o sistema operacional
ifeq ($(OS),Windows_NT)
    BINARY_EXTENSION=.exe
else
    BINARY_EXTENSION=
endif

.PHONY: all build clean test run docker-build docker-run help docs lint build-all

all: build

# Compila o projeto para a plataforma atual
build:
	@echo "Compilando para a plataforma atual..."
	go build -o $(BUILD_DIR)/$(BINARY_NAME)$(BINARY_EXTENSION) main.go

# Compila para todas as plataformas suportadas
build-all:
	@echo "Compilando para todas as plataformas..."
	# Linux AMD64
	GOOS=linux GOARCH=amd64 go build -o $(BUILD_DIR)/linux-amd64/$(BINARY_NAME) main.go
	# Linux ARM64
	GOOS=linux GOARCH=arm64 go build -o $(BUILD_DIR)/linux-arm64/$(BINARY_NAME) main.go
	# Linux ARM
	GOOS=linux GOARCH=arm go build -o $(BUILD_DIR)/linux-arm/$(BINARY_NAME) main.go
	# MacOS AMD64
	GOOS=darwin GOARCH=amd64 go build -o $(BUILD_DIR)/macos-amd64/$(BINARY_NAME) main.go
	# MacOS ARM64
	GOOS=darwin GOARCH=arm64 go build -o $(BUILD_DIR)/macos-arm64/$(BINARY_NAME) main.go
	# Windows AMD64
	GOOS=windows GOARCH=amd64 go build -o $(BUILD_DIR)/windows-amd64/$(BINARY_NAME).exe main.go
	# Windows 386
	GOOS=windows GOARCH=386 go build -o $(BUILD_DIR)/windows-386/$(BINARY_NAME).exe main.go

# Limpa os arquivos de build
clean:
	@echo "Limpando arquivos de build..."
	rm -rf $(BUILD_DIR)
	go clean
	rm -f $(BINARY_NAME)$(BINARY_EXTENSION)

# Executa os testes
test:
	@echo "Executando testes..."
	go test ./... -v

# Executa o programa
run:
	@echo "Executando o programa..."
	go run main.go

# Constrói a imagem Docker
docker-build:
	@echo "Construindo imagem Docker..."
	docker build -t $(DOCKER_IMAGE) .

# Executa o container Docker
docker-run:
	@echo "Executando container Docker..."
	docker run -p 8080:8080 $(DOCKER_IMAGE)

# Gera a documentação Swagger
docs:
	@echo "Gerando documentação Swagger..."
	swag init

# Executa o linter
lint:
	@echo "Executando linter..."
	go vet ./...
	test -z "$$(gofmt -l .)"

# Instala as dependências do projeto
deps:
	@echo "Instalando dependências..."
	go mod download
	go mod tidy

# Atualiza as dependências do projeto
deps-update:
	@echo "Atualizando dependências..."
	go get -u ./...
	go mod tidy

# Mostra a ajuda
help:
	@echo "Comandos disponíveis:"
	@echo "  make build        - Compila o projeto para a plataforma atual"
	@echo "  make build-all    - Compila para todas as plataformas suportadas"
	@echo "  make clean        - Remove arquivos de build"
	@echo "  make test         - Executa os testes"
	@echo "  make run          - Executa o programa"
	@echo "  make docker-build - Constrói a imagem Docker"
	@echo "  make docker-run   - Executa o container Docker"
	@echo "  make docs         - Gera a documentação Swagger"
	@echo "  make lint         - Executa o linter"
	@echo "  make deps         - Instala as dependências"
	@echo "  make deps-update  - Atualiza as dependências"
	@echo "  make help         - Mostra esta mensagem de ajuda"