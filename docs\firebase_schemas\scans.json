{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Scans Collection Schema", "description": "Schema para a collection 'scans' do Firebase", "type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time", "description": "Data e hora de criação do registro no formato ISO 8601"}, "descricao": {"type": "string", "description": "Descrição adicional do scan (pode estar vazio)"}, "id": {"type": "string", "description": "Identificador único do scan, geralmente um ObjectID do MongoDB"}, "location": {"type": "object", "description": "Localização geográfica onde o scan foi realizado", "properties": {"latitude": {"type": "number", "description": "Latitude da localização"}, "longitude": {"type": "number", "description": "Longitude da localização"}}, "required": ["latitude", "longitude"]}, "mac": {"type": ["string", "null"], "description": "Endereço MAC do dispositivo (pode ser nulo)"}, "modelo": {"type": "string", "description": "Modelo do veículo"}, "placa": {"type": "string", "description": "Placa do veículo"}, "processed": {"type": "boolean", "description": "Indica se o scan foi processado"}, "syncedAt": {"type": "string", "format": "date-time", "description": "Data e hora de sincronização do registro no formato ISO 8601"}, "type": {"type": "string", "description": "Tipo de operação (ex: 'login', 'logout', etc.)", "enum": ["login", "logout", "checkin", "checkout"]}, "typeScan": {"type": "string", "description": "Método de escaneamento utilizado", "enum": ["qrcode", "nfc", "manual", "bluetooth"]}, "updatedAt": {"type": "string", "format": "date-time", "description": "Data e hora da última atualização do registro"}, "userId": {"type": "string", "description": "ID do documento no Firebase que representa o usuário que realizou o scan"}, "userMID": {"type": "string", "description": "ID do MongoDB (ObjectID em formato hexadecimal) que identifica o motorista no banco de dados principal. Pode não estar presente em registros antigos."}}, "required": ["createdAt", "id", "type", "typeScan", "userId"]}