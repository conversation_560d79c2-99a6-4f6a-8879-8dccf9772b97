package config

import (
	"context"
	"log"
	"path/filepath"
	"os" // Para obter o diretório de trabalho atual

	"cloud.google.com/go/firestore"
	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/auth" // Adicionado para Firebase Authentication
	"google.golang.org/api/option"
)

// InitFirebase inicializa o app Firebase e retorna um cliente Firestore e um cliente Auth.
func InitFirebase() (*firestore.Client, *auth.Client, error) {
	ctx := context.Background()

	// Obtém o diretório de trabalho atual
	wd, err := os.Getwd()
	if err != nil {
		log.Fatalf("Erro ao obter o diretório de trabalho: %v\n", err)
		return nil, nil, err
	}

	// Constrói o caminho absoluto para o arquivo de credenciais
	// Assumindo que 'data/credencial.json' está na raiz do projeto.
	credentialPath := filepath.Join(wd, "data", "credencial.json") 
	
	// Verifique se o arquivo de credencial existe
	if _, err := os.Stat(credentialPath); os.IsNotExist(err) {
		// Tenta um caminho alternativo comum se executado de um subdiretório (ex: 'cmd/app/main.go')
		// para encontrar 'data/credencial.json' na raiz do projeto.
		projectRootGuess := filepath.Join(wd, "..", "data", "credencial.json")
		if _, errRoot := os.Stat(projectRootGuess); os.IsNotExist(errRoot) {
			log.Fatalf("Arquivo de credencial não encontrado em %s nem em %s. Verifique o caminho ou defina FIREBASE_APPLICATION_CREDENTIALS.", credentialPath, projectRootGuess)
			return nil, nil, err // Retorna o erro original da primeira tentativa
		}
		credentialPath = projectRootGuess // Usa o caminho da raiz do projeto se encontrado
		log.Printf("Arquivo de credencial encontrado na raiz do projeto: %s\n", credentialPath)
	} else {
		log.Printf("Arquivo de credencial encontrado em: %s\n", credentialPath)
	}

	sa := option.WithCredentialsFile(credentialPath)
	app, err := firebase.NewApp(ctx, nil, sa)
	if err != nil {
		log.Fatalf("Erro ao inicializar o app Firebase: %v\n", err)
		return nil, nil, err
	}

	client, err := app.Firestore(ctx)
	if err != nil {
		log.Fatalf("Erro ao obter o cliente Firestore: %v\n", err)
		return nil, nil, err
	}

	// Obter cliente Auth
	authClient, err := app.Auth(ctx)
	if err != nil {
		log.Fatalf("Erro ao obter o cliente Auth: %v\n", err)
		return nil, nil, err
	}

	log.Println("Firebase inicializado com sucesso. Clientes Firestore e Auth prontos.")
	return client, authClient, nil
}
