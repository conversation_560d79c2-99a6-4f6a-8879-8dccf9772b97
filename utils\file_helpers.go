package utils

import (
	"encoding/base64"
	"fmt"
	"log"
	"mime"
	"os"
	"path" // Adicionado para junção de caminhos de URL
	"path/filepath"
	"strings"
)

// ParsedDataURIInfo contém informações extraídas de um Data URI.
type ParsedDataURIInfo struct {
	MIMEType  string
	RawData   string // Dados base64 puros
	Extension string
}

// ParseDataURI extrai MIME type, extensão, e dados base64 puros de um Data URI.
func ParseDataURI(dataURI string) (*ParsedDataURIInfo, error) {
	if !strings.HasPrefix(dataURI, "data:") {
		return nil, fmt.Errorf("formato de URI de dados inválido: prefixo 'data:' ausente")
	}
	parts := strings.SplitN(dataURI, ",", 2)
	if len(parts) != 2 {
		return nil, fmt.Errorf("formato de URI de dados inválido: separador de vírgula ausente")
	}

	meta := parts[0] // ex: "data:image/jpeg;base64"
	rawData := parts[1]

	if !strings.Contains(meta, ";base64") {
		return nil, fmt.E<PERSON><PERSON>("URI de dados inválido: não codificado em base64 ou ';base64' ausente")
	}

	mimeType := strings.TrimSuffix(strings.TrimPrefix(meta, "data:"), ";base64")
	if mimeType == "" {
		mimeType = "application/octet-stream" // Tipo MIME padrão se não especificado
	}

	extensions, err := mime.ExtensionsByType(mimeType)
	var extension string
	if err != nil || len(extensions) == 0 {
		log.Printf("Nenhuma extensão encontrada para o tipo MIME %s, tentando fallback. Erro: %v", mimeType, err)
		switch strings.ToLower(mimeType) {
		case "image/jpeg", "image/jpg":
			extension = ".jpg"
		case "image/png":
			extension = ".png"
		case "image/gif":
			extension = ".gif"
		case "application/pdf":
			extension = ".pdf"
		default:
			log.Printf("Tipo MIME desconhecido ou sem fallback para extensão: %s. Usando '.bin'", mimeType)
			extension = ".bin"
		}
	} else {
		extension = extensions[0]
	}

	return &ParsedDataURIInfo{
		MIMEType:  mimeType,
		RawData:   rawData,
		Extension: extension,
	}, nil
}

// SaveBase64ToFile decodifica um Data URI base64 e o salva em um arquivo.
// subPathDir: ex: "veiculos/fotos" ou "motoristas/documentos/empresaID". O baseFileName será o nome do arquivo (sem extensão).
// Retorna o nome do arquivo gerado (com extensão), o tipo de conteúdo MIME e a extensão.
func SaveBase64ToFile(base64DataURI, uploadsDir, subPathDir, baseFileName string) (generatedFileName string, contentType string, extension string, err error) {
	if base64DataURI == "" {
		return "", "", "", nil // Nenhum dado para salvar
	}

	parsedInfo, err := ParseDataURI(base64DataURI)
	if err != nil {
		return "", "", "", fmt.Errorf("falha ao analisar o URI de dados: %w", err)
	}

	decodedData, err := base64.StdEncoding.DecodeString(parsedInfo.RawData)
	if err != nil {
		return "", "", "", fmt.Errorf("falha ao decodificar dados base64: %w", err)
	}

	// Nome do arquivo final: baseFileName + .extensao (ex: id_do_veiculo.jpg)
	generatedFileName = baseFileName + parsedInfo.Extension
	contentType = parsedInfo.MIMEType
	extension = parsedInfo.Extension

	// Caminho do diretório completo no sistema de arquivos: uploadsDir/subPathDir
	// O subPathDir já deve conter qualquer estrutura de subpasta necessária (ex: "veiculos/fotos" ou "motoristas/fotos/empresaID")
	fullFileSystemDir := filepath.Join(uploadsDir, subPathDir)

	if err := os.MkdirAll(fullFileSystemDir, os.ModePerm); err != nil {
		return "", "", "", fmt.Errorf("falha ao criar diretório %s: %w", fullFileSystemDir, err)
	}

	fullFileSystemPath := filepath.Join(fullFileSystemDir, generatedFileName)

	if err := os.WriteFile(fullFileSystemPath, decodedData, 0644); err != nil {
		return "", "", "", fmt.Errorf("falha ao escrever arquivo %s: %w", fullFileSystemPath, err)
	}

	return generatedFileName, contentType, extension, nil
}

// CreateFullURL constrói a URL completa para um recurso estático.
// Exemplo: apiBaseURL="http://localhost:8080", staticRoutePrefix="/static", relativePath="images/pic.jpg"
//
//	-> "http://localhost:8080/static/images/pic.jpg"
func CreateFullURL(apiBaseURL, staticRoutePrefix, relativePath string) string {
	// Garante que apiBaseURL não tenha uma barra no final
	baseURL := strings.TrimSuffix(apiBaseURL, "/")

	// Garante que staticRoutePrefix comece com uma barra e não termine com uma
	// Além disso, se staticRoutePrefix for vazio ou "/", normaliza para "/" ou "" respectivamente para path.Join funcionar corretamente
	var prefix string
	trimmedPrefix := strings.Trim(staticRoutePrefix, "/")
	if trimmedPrefix == "" { // Se era "/" ou ""
		prefix = "/" // path.Join com "/" no início garante que é a partir da raiz do host
	} else {
		prefix = "/" + trimmedPrefix
	}

	// Garante que relativePath não comece com uma barra para evitar problemas com path.Join
	relPath := strings.TrimPrefix(relativePath, "/")

	// path.Join para construir o caminho, normalizando as barras
	// Se prefix for apenas "/", e relPath for "foo/bar", resultará em "/foo/bar"
	// Se prefix for "/static" e relPath for "foo/bar", resultará em "/static/foo/bar"
	finalPath := path.Join(prefix, relPath)

	// Se o staticRoutePrefix original era vazio (ou seja, os arquivos estão na raiz do baseURL),
	// e o relativePath também era vazio (improvável, mas para cobrir),
	// path.Join("/", "") retorna "/". Se queremos apenas baseURL nesse caso, precisamos tratar.
	// No entanto, para arquivos estáticos, um relativePath vazio não faz muito sentido.
	// E se staticRoutePrefix é realmente vazio, prefix se torna "/" e finalPath será "/"+relPath.

	return baseURL + finalPath
}
